[workspace]
members = [
    "engine",
    "game"
]
resolver = "2"

[workspace.dependencies]
# Graphics and windowing
wgpu = "0.19"
winit = "0.29"
pollster = "0.3"

# Math
glam = "0.25"
bytemuck = { version = "1.14", features = ["derive"] }

# Utilities
anyhow = "1.0"
log = "0.4"
env_logger = "0.10"

# Image loading
image = "0.24"

# Asset loading
tobj = "4.0"

# Threading
rayon = "1.8"
crossbeam = "0.8"
num_cpus = "1.16"

# VR Support (OpenXR)
openxr = "0.18"
ash = "0.37"
