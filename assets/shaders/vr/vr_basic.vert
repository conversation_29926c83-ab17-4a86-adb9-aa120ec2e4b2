#version 450
#extension GL_EXT_multiview : require

// Vertex input - minimal
layout(location = 0) in vec3 inPosition;
layout(location = 1) in vec3 inNormal;
layout(location = 2) in vec2 inTexCoord;

// Uniform buffer for camera matrices
layout(binding = 0) uniform CameraUniform {
    mat4 leftEyeViewProj;
    mat4 rightEyeViewProj;
} camera;

// Push constants for per-object transform
layout(push_constant) uniform PushConstants {
    mat4 modelMatrix;
} pushConstants;

// Output to fragment shader - minimal
layout(location = 0) out vec3 fragColor;

void main() {
    // Use gl_ViewIndex to select the appropriate eye
    mat4 viewProj = (gl_ViewIndex == 0) ? camera.leftEyeViewProj : camera.rightEyeViewProj;

    // Apply model transform first, then view-projection
    vec4 worldPos = pushConstants.modelMatrix * vec4(inPosition, 1.0);
    gl_Position = viewProj * worldPos;

    // Simple solid color based on position - no lighting
    fragColor = vec3(0.8, 0.6, 0.4); // Orange-brown solid color
}
