[package]
name = "game_engine"
version = "0.1.0"
edition = "2021"
description = "A 3D game engine built with wgpu"
license = "MIT OR Apache-2.0"

[lib]
name = "game_engine"
crate-type = ["cdylib", "rlib"]

[dependencies]
# Graphics and windowing
wgpu = { workspace = true }
winit = { workspace = true }
pollster = { workspace = true }

# Math
glam = { workspace = true }
bytemuck = { workspace = true }

# Utilities
anyhow = { workspace = true }
log = { workspace = true }
env_logger = { workspace = true }

# Image loading
image = { workspace = true }

# Asset loading
tobj = { workspace = true }

# Threading
rayon = { workspace = true }
crossbeam = { workspace = true }
num_cpus = { workspace = true }

# VR Support (OpenXR) - optional dependencies
openxr = { workspace = true, optional = true }
ash = { workspace = true, optional = true }

[features]
default = []
debug = []
vr = ["openxr", "ash"]
