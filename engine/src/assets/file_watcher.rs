use std::collections::HashMap;
use std::fs;
use std::path::{ Path, PathBuf };
use std::time::UNIX_EPOCH;

/// Trait for objects that want to be notified when files change.
///
/// Implementors should specify which files they want to monitor and handle
/// file change notifications appropriately.
pub trait FileWatcher {
    /// Called when a watched file has been modified.
    ///
    /// # Arguments
    /// * `path` - The path to the file that was modified
    fn on_file_changed(&mut self, path: &Path);

    /// Returns the list of files this watcher wants to monitor.
    ///
    /// This method is called when the watcher is registered and may be called
    /// periodically to refresh the list of watched files.
    fn get_watched_files(&self) -> Vec<PathBuf>;
}

/// Central file watching manager that monitors files and triggers callbacks.
///
/// This manager coordinates multiple file watchers and efficiently checks for
/// file modifications at configurable intervals. It uses file timestamps to
/// detect changes and notifies registered watchers when their monitored files
/// are modified.
pub struct FileWatchManager {
    /// List of registered file watchers
    watchers: Vec<Box<dyn FileWatcher>>,
    /// Cache of file timestamps for change detection
    file_timestamps: HashMap<PathBuf, u64>,
    /// How often to check for file changes (in milliseconds)
    check_interval_ms: u64,
    /// When we last checked for file changes
    last_check_time: std::time::Instant,
}

impl FileWatchManager {
    /// Creates a new file watch manager with default settings.
    ///
    /// The default check interval is 500ms, which provides a good balance
    /// between responsiveness and performance.
    pub fn new() -> Self {
        Self {
            watchers: Vec::new(),
            file_timestamps: HashMap::new(),
            check_interval_ms: 500, // Check every 500ms by default
            last_check_time: std::time::Instant::now(),
        }
    }

    /// Sets how often to check for file changes.
    ///
    /// # Arguments
    /// * `interval_ms` - Check interval in milliseconds. Lower values provide
    ///   faster detection but use more CPU resources.
    pub fn set_check_interval(&mut self, interval_ms: u64) {
        self.check_interval_ms = interval_ms;
    }

    /// Registers a file watcher to monitor its specified files.
    ///
    /// The watcher's `get_watched_files()` method is called immediately to
    /// initialize the timestamp cache for all monitored files.
    ///
    /// # Arguments
    /// * `watcher` - The file watcher to register
    pub fn add_watcher(&mut self, watcher: Box<dyn FileWatcher>) {
        // Initialize timestamps for all files this watcher cares about
        for file_path in watcher.get_watched_files() {
            if let Ok(timestamp) = get_file_timestamp(&file_path) {
                self.file_timestamps.insert(file_path, timestamp);
            }
        }

        self.watchers.push(watcher);
    }

    /// Checks for file changes and notifies watchers.
    ///
    /// This method should be called regularly (typically every frame) to detect
    /// file modifications. It respects the configured check interval to avoid
    /// excessive filesystem operations.
    ///
    /// The method will:
    /// 1. Check if enough time has passed since the last check
    /// 2. Collect all files being watched by registered watchers
    /// 3. Compare current timestamps with cached values
    /// 4. Notify relevant watchers about any changed files
    pub fn update(&mut self) {
        let now = std::time::Instant::now();
        if now.duration_since(self.last_check_time).as_millis() < (self.check_interval_ms as u128) {
            return; // Not time to check yet
        }

        self.last_check_time = now;

        // Collect all files that need to be watched
        let mut all_watched_files = Vec::new();
        for watcher in &self.watchers {
            all_watched_files.extend(watcher.get_watched_files());
        }

        // Check each file for changes
        let mut changed_files = Vec::new();
        for file_path in all_watched_files {
            if let Ok(current_timestamp) = get_file_timestamp(&file_path) {
                let stored_timestamp = self.file_timestamps.get(&file_path).copied().unwrap_or(0);

                if current_timestamp != stored_timestamp {
                    // File has changed!
                    self.file_timestamps.insert(file_path.clone(), current_timestamp);
                    changed_files.push(file_path);
                }
            }
        }

        // Notify watchers about changed files
        for changed_file in changed_files {
            for watcher in &mut self.watchers {
                let watched_files = watcher.get_watched_files();
                if watched_files.contains(&changed_file) {
                    watcher.on_file_changed(&changed_file);
                }
            }
        }
    }

    /// Manually refreshes timestamps for all watched files.
    ///
    /// This method clears the timestamp cache and rebuilds it by querying
    /// all registered watchers for their monitored files. Useful for
    /// initialization or when you want to reset the change detection state.
    pub fn refresh_all_timestamps(&mut self) {
        self.file_timestamps.clear();

        for watcher in &self.watchers {
            for file_path in watcher.get_watched_files() {
                if let Ok(timestamp) = get_file_timestamp(&file_path) {
                    self.file_timestamps.insert(file_path, timestamp);
                }
            }
        }
    }
}

impl Default for FileWatchManager {
    fn default() -> Self {
        Self::new()
    }
}

/// Gets the last modified timestamp of a file in seconds since Unix epoch.
///
/// # Arguments
/// * `path` - The path to the file to check
///
/// # Returns
/// * `Ok(u64)` - The timestamp in seconds since Unix epoch
/// * `Err(std::io::Error)` - If the file cannot be accessed or metadata cannot be read
///
/// # Errors
/// This function will return an error if:
/// * The file does not exist or cannot be accessed
/// * The file's metadata cannot be read
/// * The system time appears to have gone backwards relative to Unix epoch
fn get_file_timestamp(path: &Path) -> Result<u64, std::io::Error> {
    let metadata = fs::metadata(path)?;
    let modified_time = metadata.modified()?;
    let timestamp = modified_time
        .duration_since(UNIX_EPOCH)
        .map_err(|_| std::io::Error::new(std::io::ErrorKind::Other, "Time went backwards"))?
        .as_secs();
    Ok(timestamp)
}
