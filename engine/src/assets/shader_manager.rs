use super::file_watcher::FileWatcher;
use std::collections::HashMap;
use std::fs;
use std::path::{ Path, PathBuf };
use std::time::SystemTime;
use wgpu::{ ComputePipeline, ShaderModule };

/// Complete shader program with vertex and fragment shaders
#[derive(Debug)]
pub struct ShaderProgram {
    pub name: String,
    pub vertex_path: PathBuf,
    pub fragment_path: PathBuf,
    pub vertex_module: Option<ShaderModule>,
    pub fragment_module: Option<ShaderModule>,
}

/// Compute shader program with pipeline
#[derive(Debug)]
pub struct ComputeShaderProgram {
    pub name: String,
    pub compute_path: PathBuf,
    pub compute_module: Option<ShaderModule>,
    pub pipeline: Option<ComputePipeline>,
    pub bind_group_layout_entries: Vec<Vec<wgpu::BindGroupLayoutEntry>>,
}

/// Manages shader compilation, caching, and hot-reloading
pub struct ShaderManager {
    device: std::sync::Arc<wgpu::Device>,
    shader_programs: HashMap<String, ShaderProgram>,
    compute_programs: HashMap<String, ComputeShaderProgram>,
    shader_directory: PathBuf,
    watched_files: Vec<PathBuf>,
    file_mod_times: HashMap<PathBuf, SystemTime>,
}

impl ShaderManager {
    /// Creates a new shader manager
    pub fn new(device: std::sync::Arc<wgpu::Device>, shader_directory: impl Into<PathBuf>) -> Self {
        let shader_dir = shader_directory.into();
        println!("Initializing ShaderManager with directory: {:?}", shader_dir);

        Self {
            device,
            shader_programs: HashMap::new(),
            compute_programs: HashMap::new(),
            shader_directory: shader_dir,
            watched_files: Vec::new(),
            file_mod_times: HashMap::new(),
        }
    }

    /// Registers the error shader program (fallback for compilation failures)
    pub fn register_error_shader(&mut self) -> Result<(), String> {
        self.register_shader_program("error", "error.vert.wgsl", "error.frag.wgsl")
    }

    /// Registers a shader program (vertex + fragment pair)
    pub fn register_shader_program(
        &mut self,
        name: &str,
        vertex_filename: &str,
        fragment_filename: &str
    ) -> Result<(), String> {
        let vertex_path = self.shader_directory.join(vertex_filename);
        let fragment_path = self.shader_directory.join(fragment_filename);

        // Verify files exist
        if !vertex_path.exists() {
            return Err(format!("Vertex shader not found: {:?}", vertex_path));
        }
        if !fragment_path.exists() {
            return Err(format!("Fragment shader not found: {:?}", fragment_path));
        }

        let mut program = ShaderProgram {
            name: name.to_string(),
            vertex_path: vertex_path.clone(),
            fragment_path: fragment_path.clone(),
            vertex_module: None,
            fragment_module: None,
        };

        // Compile shaders initially
        if let Err(e) = self.compile_shader_program(&mut program) {
            println!("ERROR: Failed to compile shader program '{}': {}", name, e);
            return Err(e);
        }

        // Add to watched files and track modification times
        if let Ok(metadata) = fs::metadata(&vertex_path) {
            if let Ok(modified) = metadata.modified() {
                self.file_mod_times.insert(vertex_path.clone(), modified);
            }
        }
        if let Ok(metadata) = fs::metadata(&fragment_path) {
            if let Ok(modified) = metadata.modified() {
                self.file_mod_times.insert(fragment_path.clone(), modified);
            }
        }

        self.watched_files.push(vertex_path);
        self.watched_files.push(fragment_path);

        self.shader_programs.insert(name.to_string(), program);
        println!("Registered shader program: {}", name);

        Ok(())
    }

    /// Gets a compiled shader program
    pub fn get_shader_program(&self, name: &str) -> Option<&ShaderProgram> {
        self.shader_programs.get(name)
    }

    /// Gets vertex shader module
    pub fn get_vertex_shader(&self, program_name: &str) -> Option<&ShaderModule> {
        self.shader_programs.get(program_name)?.vertex_module.as_ref()
    }

    /// Gets fragment shader module
    pub fn get_fragment_shader(&self, program_name: &str) -> Option<&ShaderModule> {
        self.shader_programs.get(program_name)?.fragment_module.as_ref()
    }

    /// Lists all available shader programs
    pub fn list_shader_programs(&self) -> Vec<&str> {
        self.shader_programs
            .keys()
            .map(|s| s.as_str())
            .collect()
    }

    /// Registers a compute shader program
    pub fn register_compute_shader(
        &mut self,
        name: &str,
        compute_filename: &str,
        bind_group_layouts: &[&wgpu::BindGroupLayout]
    ) -> Result<(), String> {
        let compute_path = self.shader_directory.join(compute_filename);

        // Verify file exists
        if !compute_path.exists() {
            return Err(format!("Compute shader not found: {:?}", compute_path));
        }

        let mut program = ComputeShaderProgram {
            name: name.to_string(),
            compute_path: compute_path.clone(),
            compute_module: None,
            pipeline: None,
            bind_group_layout_entries: Vec::new(), // For now, we'll handle hot-reload differently
        };

        // Compile compute shader initially
        if let Err(e) = self.compile_compute_shader(&mut program, bind_group_layouts) {
            println!("ERROR: Failed to compile compute shader '{}': {}", name, e);
            return Err(e);
        }

        // Add to watched files and track modification times
        if let Ok(metadata) = fs::metadata(&compute_path) {
            if let Ok(modified) = metadata.modified() {
                self.file_mod_times.insert(compute_path.clone(), modified);
            }
        }

        self.watched_files.push(compute_path);
        self.compute_programs.insert(name.to_string(), program);
        println!("Registered compute shader: {}", name);

        Ok(())
    }

    /// Gets a compute shader program
    pub fn get_compute_program(&self, name: &str) -> Option<&ComputeShaderProgram> {
        self.compute_programs.get(name)
    }

    /// Gets compute shader module
    pub fn get_compute_shader(&self, program_name: &str) -> Option<&ShaderModule> {
        self.compute_programs.get(program_name)?.compute_module.as_ref()
    }

    /// Gets compute pipeline
    pub fn get_compute_pipeline(&self, program_name: &str) -> Option<&ComputePipeline> {
        self.compute_programs.get(program_name)?.pipeline.as_ref()
    }

    /// Lists all available compute shader programs
    pub fn list_compute_programs(&self) -> Vec<&str> {
        self.compute_programs
            .keys()
            .map(|s| s.as_str())
            .collect()
    }

    /// Checks if a compute shader program exists
    pub fn has_compute_program(&self, program_name: &str) -> bool {
        self.compute_programs.contains_key(program_name)
    }

    /// Compiles a shader program from source files
    fn compile_shader_program(&mut self, program: &mut ShaderProgram) -> Result<(), String> {
        let (vertex_module, fragment_module) = self.compile_shader_files(
            &program.vertex_path,
            &program.fragment_path
        )?;

        program.vertex_module = Some(vertex_module);
        program.fragment_module = Some(fragment_module);

        println!("Successfully compiled shader program: {}", program.name);
        Ok(())
    }

    /// Compiles a compute shader program from source file
    fn compile_compute_shader(
        &mut self,
        program: &mut ComputeShaderProgram,
        bind_group_layouts: &[&wgpu::BindGroupLayout]
    ) -> Result<(), String> {
        // Read compute shader source
        let compute_source = fs
            ::read_to_string(&program.compute_path)
            .map_err(|e|
                format!("Failed to read compute shader {:?}: {}", program.compute_path, e)
            )?;

        // Compile compute shader
        let compute_module = self
            .compile_shader_source(&compute_source, "compute")
            .map_err(|e| format!("Compute shader compilation failed: {}", e))?;

        // Create compute pipeline
        let pipeline_layout = self.device.create_pipeline_layout(
            &(wgpu::PipelineLayoutDescriptor {
                label: Some(&format!("{}_compute_pipeline_layout", program.name)),
                bind_group_layouts,
                push_constant_ranges: &[],
            })
        );

        let pipeline = self.device.create_compute_pipeline(
            &(wgpu::ComputePipelineDescriptor {
                label: Some(&format!("{}_compute_pipeline", program.name)),
                layout: Some(&pipeline_layout),
                module: &compute_module,
                entry_point: "main",
            })
        );

        program.compute_module = Some(compute_module);
        program.pipeline = Some(pipeline);

        println!("Successfully compiled compute shader: {}", program.name);
        Ok(())
    }

    /// Compiles WGSL source code into a shader module
    fn compile_shader_source(
        &self,
        source: &str,
        shader_type: &str
    ) -> Result<ShaderModule, String> {
        let label = format!("{}_shader", shader_type);
        let shader_desc = wgpu::ShaderModuleDescriptor {
            label: Some(&label),
            source: wgpu::ShaderSource::Wgsl(source.into()),
        };

        // WGPU panics on shader compilation errors, so we need to catch the panic
        let result = std::panic::catch_unwind(
            std::panic::AssertUnwindSafe(|| { self.device.create_shader_module(shader_desc) })
        );

        match result {
            Ok(module) => Ok(module),
            Err(panic_info) => {
                // Extract error message from panic if possible
                let error_msg = if let Some(s) = panic_info.downcast_ref::<String>() {
                    s.clone()
                } else if let Some(s) = panic_info.downcast_ref::<&str>() {
                    s.to_string()
                } else {
                    format!("Shader compilation failed for {} shader (unknown error)", shader_type)
                };

                println!("🚨 CAUGHT SHADER PANIC: {}", error_msg);
                Err(format!("Shader compilation failed: {}", error_msg))
            }
        }
    }

    /// Reloads a specific shader file
    fn reload_shader_file(&mut self, file_path: &Path) {
        println!("Reloading shader file: {:?}", file_path);

        // Find which shader program(s) use this file
        let mut programs_to_reload = Vec::new();
        for (name, program) in &self.shader_programs {
            if program.vertex_path == file_path || program.fragment_path == file_path {
                programs_to_reload.push((
                    name.clone(),
                    program.vertex_path.clone(),
                    program.fragment_path.clone(),
                ));
            }
        }

        // Reload affected programs
        for (program_name, vertex_path, fragment_path) in programs_to_reload {
            match self.compile_shader_files(&vertex_path, &fragment_path) {
                Ok((vertex_module, fragment_module)) => {
                    if let Some(program) = self.shader_programs.get_mut(&program_name) {
                        program.vertex_module = Some(vertex_module);
                        program.fragment_module = Some(fragment_module);

                    }
                }
                Err(_) => {

                    // Keep the old shader modules - don't replace with broken ones
                }
            }
        }
    }

    /// Compiles shader files and returns the modules
    fn compile_shader_files(
        &self,
        vertex_path: &Path,
        fragment_path: &Path
    ) -> Result<(ShaderModule, ShaderModule), String> {
        // Read vertex shader source
        let vertex_source = fs
            ::read_to_string(vertex_path)
            .map_err(|e| format!("Failed to read vertex shader {:?}: {}", vertex_path, e))?;

        // Read fragment shader source
        let fragment_source = fs
            ::read_to_string(fragment_path)
            .map_err(|e| format!("Failed to read fragment shader {:?}: {}", fragment_path, e))?;

        // Compile vertex shader
        let vertex_module = self
            .compile_shader_source(&vertex_source, "vertex")
            .map_err(|e| format!("Vertex shader compilation failed: {}", e))?;

        // Compile fragment shader
        let fragment_module = self
            .compile_shader_source(&fragment_source, "fragment")
            .map_err(|e| format!("Fragment shader compilation failed: {}", e))?;

        Ok((vertex_module, fragment_module))
    }

    /// Checks if a shader program exists
    pub fn has_shader_program(&self, program_name: &str) -> bool {
        self.shader_programs.contains_key(program_name)
    }

    /// Checks for file changes and reloads shaders if needed
    /// Returns (updated_programs, failed_programs)
    pub fn check_for_updates(&mut self) -> (Vec<String>, Vec<String>) {
        let mut updated_programs = Vec::new();
        let mut failed_programs = Vec::new();
        let mut files_to_reload = Vec::new();

        // Check each watched file for modifications
        for file_path in &self.watched_files.clone() {
            if let Ok(metadata) = fs::metadata(file_path) {
                if let Ok(current_modified) = metadata.modified() {
                    // Check if we have a stored modification time
                    if let Some(&stored_modified) = self.file_mod_times.get(file_path) {
                        // Compare modification times
                        if current_modified > stored_modified {
                            files_to_reload.push(file_path.clone());
                            self.file_mod_times.insert(file_path.clone(), current_modified);
                        }
                    } else {
                        // First time seeing this file, store its modification time
                        self.file_mod_times.insert(file_path.clone(), current_modified);
                    }
                }
            }
        }

        // Reload programs that use changed files
        for file_path in files_to_reload {
            // Collect render shader programs that need updating first
            let mut programs_to_update = Vec::new();
            for (program_name, program) in &self.shader_programs {
                if program.vertex_path == file_path || program.fragment_path == file_path {
                    programs_to_update.push((
                        program_name.clone(),
                        program.vertex_path.clone(),
                        program.fragment_path.clone(),
                    ));
                }
            }

            // Skip compute shader collection - compute shaders are not hot-swappable

            // Update render shader programs
            for (program_name, vertex_path, fragment_path) in programs_to_update {
                match self.compile_shader_files(&vertex_path, &fragment_path) {
                    Ok((vertex_module, fragment_module)) => {


                        // Update the program with new modules
                        if let Some(program_mut) = self.shader_programs.get_mut(&program_name) {
                            program_mut.vertex_module = Some(vertex_module);
                            program_mut.fragment_module = Some(fragment_module);
                            if !updated_programs.contains(&program_name) {
                                updated_programs.push(program_name);
                            }
                        }
                    }
                    Err(_) => {

                        if !failed_programs.contains(&program_name) {
                            failed_programs.push(program_name);
                        }
                    }
                }
            }

            // Skip compute shader hot-reload - compute shaders are not hot-swappable
            // Compute shaders will only be compiled once at startup for better performance
        }

        (updated_programs, failed_programs)
    }
}

impl FileWatcher for ShaderManager {
    fn on_file_changed(&mut self, path: &Path) {
        // Check if this is a shader file we care about
        if let Some(extension) = path.extension() {
            if extension == "wgsl" {
                self.reload_shader_file(path);
            }
        }
    }

    fn get_watched_files(&self) -> Vec<PathBuf> {
        self.watched_files.clone()
    }
}
