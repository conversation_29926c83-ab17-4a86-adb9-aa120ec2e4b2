use crate::profiler::Profiler;
use crate::graphics::Render<PERSON>;
use std::time::Duration;


#[derive(Debug, Clone)]
pub struct DebugOverlayConfig {
    pub enabled: bool,
    pub show_fps: bool,
    pub show_frame_time: bool,
    pub show_profiler: bool,
    pub show_memory: bool,
    pub max_profiler_entries: usize,
    pub update_interval: Duration,
}

impl Default for DebugOverlayConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            show_fps: true,
            show_frame_time: true,
            show_profiler: true,
            show_memory: true,
            max_profiler_entries: 10,
            update_interval: Duration::from_millis(100), // Update 10 times per second
        }
    }
}


pub struct DebugOverlay {
    config: DebugOverlayConfig,
    last_update: std::time::Instant,
    cached_text: String,
    fps_history: Vec<f32>,
    frame_time_history: Vec<f32>,
    max_history_size: usize,
}

impl DebugOverlay {
    pub fn new(config: DebugOverlayConfig) -> Self {
        Self {
            config,
            last_update: std::time::Instant::now(),
            cached_text: String::new(),
            fps_history: Vec::new(),
            frame_time_history: Vec::new(),
            max_history_size: 60, // 1 second at 60fps
        }
    }


    pub fn update(&mut self, profiler: &Profiler, renderer: &Renderer) {
        if !self.config.enabled {
            return;
        }

        let now = std::time::Instant::now();
        if now.duration_since(self.last_update) < self.config.update_interval {
            return;
        }
        self.last_update = now;

        // Update history
        let current_fps = profiler.fps();
        let current_frame_time = profiler.frame_duration().as_secs_f32() * 1000.0;

        self.fps_history.push(current_fps);
        self.frame_time_history.push(current_frame_time);

        if self.fps_history.len() > self.max_history_size {
            self.fps_history.remove(0);
        }
        if self.frame_time_history.len() > self.max_history_size {
            self.frame_time_history.remove(0);
        }

        // Generate debug text
        self.cached_text = self.generate_debug_text(profiler, renderer);
    }


    fn generate_debug_text(&self, profiler: &Profiler, _renderer: &Renderer) -> String {
        let mut text = String::new();

        // FPS and frame time
        if self.config.show_fps || self.config.show_frame_time {
            let current_fps = profiler.fps();
            let current_frame_time = profiler.frame_duration().as_secs_f32() * 1000.0;

            if self.config.show_fps {
                let avg_fps = if !self.fps_history.is_empty() {
                    self.fps_history.iter().sum::<f32>() / self.fps_history.len() as f32
                } else {
                    current_fps
                };
                text.push_str(&format!("FPS: {:.1} (avg: {:.1})\n", current_fps, avg_fps));
            }

            if self.config.show_frame_time {
                let avg_frame_time = if !self.frame_time_history.is_empty() {
                    self.frame_time_history.iter().sum::<f32>() / self.frame_time_history.len() as f32
                } else {
                    current_frame_time
                };
                text.push_str(&format!("Frame Time: {:.2}ms (avg: {:.2}ms)\n", current_frame_time, avg_frame_time));
            }
            text.push('\n');
        }

        // Profiler information
        if self.config.show_profiler && profiler.is_enabled() {
            text.push_str("=== PROFILER ===\n");
            let stats = profiler.get_stats();

            if stats.is_empty() {
                text.push_str("No profiling data available\n");
            } else {
                text.push_str("Section                 | Cur (ms) | % Frame | Avg (ms) | Min/Max (ms)\n");
                text.push_str("------------------------|----------|---------|----------|-------------\n");

                for (i, stat) in stats.iter().enumerate() {
                    if i >= self.config.max_profiler_entries {
                        break;
                    }

                    // Calculate current frame time from percentage and frame time
                    let frame_time_ms = profiler.frame_duration().as_secs_f32() * 1000.0;
                    let current_time = if frame_time_ms > 0.0 {
                        (stat.percentage_of_frame / 100.0) * frame_time_ms
                    } else {
                        0.0
                    };

                    let avg_time = stat.average_time.as_secs_f32() * 1000.0;
                    let min_time = stat.min_time.as_secs_f32() * 1000.0;
                    let max_time = stat.max_time.as_secs_f32() * 1000.0;

                    // Only show if percentage is reasonable (< 200% to account for measurement overhead)
                    if stat.percentage_of_frame <= 200.0 {
                        text.push_str(&format!(
                            "{:<23} | {:>8.2} | {:>6.1}% | {:>6.2} | {:>4.2}/{:<4.2}\n",
                            self.truncate_string(&stat.name, 23),
                            current_time,
                            stat.percentage_of_frame,
                            avg_time,
                            min_time,
                            max_time
                        ));
                    }
                }

                if stats.len() > self.config.max_profiler_entries {
                    text.push_str(&format!("... and {} more entries\n", stats.len() - self.config.max_profiler_entries));
                }
            }
            text.push('\n');
        }

        // Memory information
        if self.config.show_memory {
            text.push_str("=== MEMORY ===\n");
            let profiler_memory = profiler.memory_usage();
            text.push_str(&format!("Profiler Memory: {:.2} KB\n", profiler_memory as f32 / 1024.0));

            // VRAM usage information
            let vram = profiler.vram_usage();
            text.push_str(&format!("VRAM Total: {:.2} MB\n", vram.total as f32 / (1024.0 * 1024.0)));
            text.push_str(&format!("VRAM Buffers: {:.2} MB\n", vram.buffers as f32 / (1024.0 * 1024.0)));
            text.push_str(&format!("VRAM Textures: {:.2} MB\n", vram.textures as f32 / (1024.0 * 1024.0)));

            text.push('\n');
        }



        text
    }

    /// Truncate string to specified length
    fn truncate_string(&self, s: &str, max_len: usize) -> String {
        if s.len() <= max_len {
            s.to_string()
        } else {
            format!("{}...", &s[..max_len.saturating_sub(3)])
        }
    }

    /// Get the cached debug text
    pub fn get_text(&self) -> &str {
        if self.config.enabled {
            &self.cached_text
        } else {
            ""
        }
    }

    /// Get configuration
    pub fn config(&self) -> &DebugOverlayConfig {
        &self.config
    }

    /// Update configuration
    pub fn set_config(&mut self, config: DebugOverlayConfig) {
        self.config = config;
    }

    /// Toggle overlay visibility
    pub fn toggle(&mut self) {
        self.config.enabled = !self.config.enabled;
    }

    /// Enable/disable overlay
    pub fn set_enabled(&mut self, enabled: bool) {
        self.config.enabled = enabled;
    }

    /// Check if overlay is enabled
    pub fn is_enabled(&self) -> bool {
        self.config.enabled
    }

}

impl Default for DebugOverlay {
    fn default() -> Self {
        Self::new(DebugOverlayConfig::default())
    }
}
