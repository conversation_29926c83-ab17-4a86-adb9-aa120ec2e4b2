use glam::{Mat4, Vec3, Quat};


#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct Camera {

    position: Vec3,

    rotation: Quat,

    fov: f32,

    aspect_ratio: f32,

    near: f32,

    far: f32,
}

impl Camera {

    pub fn new(aspect_ratio: f32) -> Self {
        Self {
            position: Vec3::new(0.0, 0.0, 3.0),
            rotation: Quat::IDENTITY,
            fov: 45.0_f32.to_radians(),
            aspect_ratio,
            near: 0.1,
            far: 1000.0,
        }
    }


    pub fn with_settings(
        position: Vec3,
        rotation: Quat,
        fov_degrees: f32,
        aspect_ratio: f32,
        near: f32,
        far: f32,
    ) -> Self {
        Self {
            position,
            rotation,
            fov: fov_degrees.to_radians(),
            aspect_ratio,
            near,
            far,
        }
    }


    pub fn set_position(&mut self, position: Vec3) {
        self.position = position;
    }


    pub fn position(&self) -> Vec3 {
        self.position
    }


    pub fn translate(&mut self, offset: Vec3) {
        self.position += offset;
    }

    /// Sets the camera rotation using a quaternion
    pub fn set_rotation(&mut self, rotation: Quat) {
        self.rotation = rotation;
    }

    /// Sets the camera rotation using Euler angles (pitch, yaw, roll in radians)
    pub fn set_rotation_euler(&mut self, pitch: f32, yaw: f32, roll: f32) {
        self.rotation = Quat::from_euler(glam::EulerRot::YXZ, yaw, pitch, roll);
    }

    /// Sets the camera rotation using Euler angles in degrees
    pub fn set_rotation_degrees(&mut self, pitch_deg: f32, yaw_deg: f32, roll_deg: f32) {
        self.set_rotation_euler(
            pitch_deg.to_radians(),
            yaw_deg.to_radians(),
            roll_deg.to_radians(),
        );
    }

    /// Gets the camera rotation as a quaternion
    pub fn rotation(&self) -> Quat {
        self.rotation
    }

    /// Rotates the camera by additional Euler angles (additive)
    pub fn rotate_euler(&mut self, pitch: f32, yaw: f32, roll: f32) {
        let additional_rotation = Quat::from_euler(glam::EulerRot::YXZ, yaw, pitch, roll);
        self.rotation = self.rotation * additional_rotation;
    }

    /// Rotates the camera by additional Euler angles in degrees (additive)
    pub fn rotate_degrees(&mut self, pitch_deg: f32, yaw_deg: f32, roll_deg: f32) {
        self.rotate_euler(
            pitch_deg.to_radians(),
            yaw_deg.to_radians(),
            roll_deg.to_radians(),
        );
    }

    /// Rotates the camera with pitch clamping to prevent over-rotation
    /// Pitch is clamped between -90 and +90 degrees
    pub fn rotate_euler_clamped(&mut self, pitch: f32, yaw: f32, roll: f32) {
        // Extract current Euler angles
        let (current_yaw, current_pitch, current_roll) = self.rotation.to_euler(glam::EulerRot::YXZ);

        // Apply yaw and roll normally (no clamping needed)
        let new_yaw = current_yaw + yaw;
        let new_roll = current_roll + roll;

        // Apply pitch with clamping
        let new_pitch = (current_pitch + pitch).clamp(-std::f32::consts::FRAC_PI_2, std::f32::consts::FRAC_PI_2);

        // Set the new rotation
        self.rotation = Quat::from_euler(glam::EulerRot::YXZ, new_yaw, new_pitch, new_roll);
    }

    /// Rotates the camera with pitch clamping using degrees
    pub fn rotate_degrees_clamped(&mut self, pitch_deg: f32, yaw_deg: f32, roll_deg: f32) {
        self.rotate_euler_clamped(
            pitch_deg.to_radians(),
            yaw_deg.to_radians(),
            roll_deg.to_radians(),
        );
    }

    /// Sets both position and rotation at once
    pub fn set_transform(&mut self, position: Vec3, rotation: Quat) {
        self.position = position;
        self.rotation = rotation;
    }

    /// Sets both position and rotation using Euler angles
    pub fn set_transform_euler(&mut self, position: Vec3, pitch: f32, yaw: f32, roll: f32) {
        self.position = position;
        self.set_rotation_euler(pitch, yaw, roll);
    }

    /// Sets both position and rotation using Euler angles in degrees
    pub fn set_transform_degrees(&mut self, position: Vec3, pitch_deg: f32, yaw_deg: f32, roll_deg: f32) {
        self.position = position;
        self.set_rotation_degrees(pitch_deg, yaw_deg, roll_deg);
    }

    /// Sets the field of view in degrees
    pub fn set_fov_degrees(&mut self, fov_degrees: f32) {
        self.fov = fov_degrees.to_radians();
    }

    /// Sets the field of view in radians
    pub fn set_fov(&mut self, fov_radians: f32) {
        self.fov = fov_radians;
    }

    /// Gets the field of view in degrees
    pub fn fov_degrees(&self) -> f32 {
        self.fov.to_degrees()
    }

    /// Gets the field of view in radians
    pub fn fov(&self) -> f32 {
        self.fov
    }

    /// Sets the aspect ratio (width / height)
    pub fn set_aspect_ratio(&mut self, aspect_ratio: f32) {
        self.aspect_ratio = aspect_ratio;
    }

    /// Gets the aspect ratio
    pub fn aspect_ratio(&self) -> f32 {
        self.aspect_ratio
    }

    /// Sets near and far clipping planes
    pub fn set_clipping_planes(&mut self, near: f32, far: f32) {
        self.near = near;
        self.far = far;
    }

    /// Gets the near clipping plane distance
    pub fn near(&self) -> f32 {
        self.near
    }

    /// Gets the far clipping plane distance
    pub fn far(&self) -> f32 {
        self.far
    }

    /// Sets only the near clipping plane
    pub fn set_near(&mut self, near: f32) {
        self.near = near;
    }

    /// Sets only the far clipping plane
    pub fn set_far(&mut self, far: f32) {
        self.far = far;
    }

    /// Gets the forward direction vector projected onto the horizontal plane (ignores pitch)
    /// This is useful for FPS-style movement where forward/backward should always be horizontal
    /// Works correctly even with extreme pitch angles (beyond ±90 degrees)
    pub fn forward(&self) -> Vec3 {
        // More robust approach: use the right vector to determine horizontal forward direction
        // The right vector is always reliable and doesn't suffer from the same precision issues
        let right = self.rotation * Vec3::X;

        // Project right vector onto horizontal plane and normalize
        let horizontal_right = Vec3::new(right.x, 0.0, right.z);

        // Handle edge case where right vector is also vertical (shouldn't happen in normal camera usage)
        if horizontal_right.length_squared() < 0.0001 {
            // Fallback to a default forward direction (negative Z)
            Vec3::new(0.0, 0.0, -1.0)
        } else {
            let normalized_right = horizontal_right.normalize();
            // Forward is 90 degrees clockwise from right in the horizontal plane
            // To rotate right vector 90 degrees clockwise: (x, z) -> (z, -x)
            Vec3::new(normalized_right.z, 0.0, -normalized_right.x)
        }
    }

    /// Gets the right direction vector projected onto the horizontal plane (ignores pitch)
    /// This is useful for FPS-style movement where left/right should always be horizontal
    /// Works correctly even with extreme pitch angles (beyond ±90 degrees)
    pub fn right(&self) -> Vec3 {
        // Since we already have the forward direction, right is just forward rotated 90 degrees clockwise
        let forward = self.forward();
        // To rotate 90 degrees clockwise in horizontal plane: (x, z) -> (z, -x)
        Vec3::new(forward.z, 0.0, -forward.x)
    }

    /// Gets the up direction vector
    pub fn up(&self) -> Vec3 {
        self.rotation * Vec3::Y
    }

    /// Generates the view matrix for rendering
    pub fn view_matrix(&self) -> Mat4 {
        let translation = Mat4::from_translation(-self.position);
        let rotation = Mat4::from_quat(self.rotation.conjugate());
        rotation * translation
    }

    /// Generates the projection matrix for rendering
    pub fn projection_matrix(&self) -> Mat4 {
        Mat4::perspective_rh(self.fov, self.aspect_ratio, self.near, self.far)
    }

    /// Generates the combined view-projection matrix
    pub fn view_projection_matrix(&self) -> Mat4 {
        self.projection_matrix() * self.view_matrix()
    }

    /// Looks at a target position (sets rotation to look at target)
    pub fn look_at(&mut self, target: Vec3, up: Vec3) {
        let forward = (target - self.position).normalize();
        let right = forward.cross(up).normalize();
        let up = right.cross(forward).normalize();

        let rotation_matrix = Mat4::from_cols(
            right.extend(0.0),
            up.extend(0.0),
            (-forward).extend(0.0), // Negate forward for right-handed system
            Vec3::ZERO.extend(1.0),
        );

        self.rotation = Quat::from_mat4(&rotation_matrix);
    }

    /// Resets camera to default position and rotation
    pub fn reset(&mut self) {
        self.position = Vec3::new(0.0, 0.0, 3.0);
        self.rotation = Quat::IDENTITY;
    }
}

impl Default for Camera {
    fn default() -> Self {
        Self::new(16.0 / 9.0) // Default 16:9 aspect ratio
    }
}
