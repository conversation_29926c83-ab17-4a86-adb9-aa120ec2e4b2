use crate::graphics::{<PERSON><PERSON><PERSON>, RendererConfig, GraphicsBackend};
use crate::input::{InputManager, KeybindManager};
use crate::{log_error, log_info, log_warning};
use crate::time::TimeManager;
use crate::profiler::Profiler;
use crate::debug::{DebugOverlay, DebugOverlayConfig};
use crate::vr::{Vr<PERSON>enderer, VrConfig, VrSceneData, VrDrawCommand, VrWindowManager, VrEye};
use std::sync::Arc;
use winit::{
    event::{Event, WindowEvent, MouseButton},
    event_loop::EventLoop,
    window::{Window, WindowBuilder, Fullscreen, CursorGrabMode},
    monitor::VideoMode,
    dpi::LogicalSize,
};

/// Fullscreen configuration options
#[derive(Debug, Clone, PartialEq)]
pub enum FullscreenMode {
    Windowed,
    BorderlessFullscreen,
    ExclusiveFullscreen(VideoMode),
    ExclusiveFullscreenAuto,
}

impl Default for FullscreenMode {
    fn default() -> Self {
        FullscreenMode::Windowed
    }
}

impl FullscreenMode {
    /// Converts FullscreenMode to winit Fullscreen option
    pub fn to_winit_fullscreen(&self, primary_monitor: Option<winit::monitor::MonitorHandle>) -> Option<Fullscreen> {
        match self {
            FullscreenMode::Windowed => None,
            FullscreenMode::BorderlessFullscreen => {
                primary_monitor.map(|monitor| Fullscreen::Borderless(Some(monitor)))
            }
            FullscreenMode::ExclusiveFullscreen(video_mode) => {
                Some(Fullscreen::Exclusive(video_mode.clone()))
            }
            FullscreenMode::ExclusiveFullscreenAuto => {
                primary_monitor.and_then(|monitor| {
                    monitor.video_modes().next().map(Fullscreen::Exclusive)
                })
            }
        }
    }
}

/// Engine configuration
#[derive(Debug, Clone)]
pub struct EngineConfig {
    pub renderer_config: RendererConfig,
    pub fullscreen_mode: FullscreenMode,
    pub window_title: String,
    pub window_size: LogicalSize<u32>,
    pub debug_overlay_config: DebugOverlayConfig,
    pub profiler_enabled: bool,
    pub vr_config: VrConfig,
}

impl Default for EngineConfig {
    fn default() -> Self {
        Self {
            renderer_config: RendererConfig::default(),
            fullscreen_mode: FullscreenMode::default(),
            window_title: "Game Engine".to_string(),
            window_size: LogicalSize::new(1280, 720),
            debug_overlay_config: DebugOverlayConfig::default(),
            profiler_enabled: true,
            vr_config: VrConfig::default(),
        }
    }
}

/// Main engine instance managing all subsystems
pub struct Engine {
    pub window: Arc<Window>,
    pub renderer: Renderer,
    pub input_manager: InputManager,
    pub keybind_manager: KeybindManager,
    pub time_manager: TimeManager,
    pub running: bool,
    current_fullscreen_mode: FullscreenMode,
    pub profiler: Profiler,
    pub debug_overlay: DebugOverlay,
    mouse_grabbed: bool,
    pub vr_renderer: VrRenderer,
    pub vr_window_manager: VrWindowManager,
}

impl Engine {
    pub async fn new(event_loop: &EventLoop<()>) -> anyhow::Result<Self> {
        Self::new_with_config(event_loop, EngineConfig::default()).await
    }

    pub async fn new_with_config(event_loop: &EventLoop<()>, config: EngineConfig) -> anyhow::Result<Self> {
        // Get primary monitor for fullscreen support
        let primary_monitor = event_loop.primary_monitor();
        let fullscreen = config.fullscreen_mode.to_winit_fullscreen(primary_monitor);

        let window = Arc::new(WindowBuilder::new()
            .with_title(&config.window_title)
            .with_inner_size(config.window_size)
            .with_fullscreen(fullscreen)
            .build(event_loop)?);

        let renderer = Renderer::new_with_config(window.clone(), config.renderer_config.clone()).await?;
        let input_manager = InputManager::new();
        let time_manager = TimeManager::new();

        let mut profiler = Profiler::new();
        profiler.set_enabled(config.profiler_enabled);
        let debug_overlay = DebugOverlay::new(config.debug_overlay_config);

        // Initialize VR renderer if enabled
        let mut vr_renderer = match VrRenderer::new(config.vr_config, config.renderer_config.graphics_backend) {
            Ok(vr) => {
                log_info!("VR renderer initialized successfully");
                vr
            }
            Err(e) => {
                log_warning!("VR renderer initialization failed (no headset connected?): {}", e);
                log_info!("Continuing with desktop-only rendering");
                // Create a stub VR renderer for desktop-only mode
                VrRenderer::new(VrConfig { enabled: false, ..Default::default() }, config.renderer_config.graphics_backend)?
            }
        };

        // Initialize VR texture views if VR is enabled
        if vr_renderer.is_enabled() {
            if let Err(e) = vr_renderer.initialize_wgpu_texture_views(renderer.device()) {
                log_warning!("Failed to initialize VR texture views: {}", e);
            } else {
                log_info!("VR texture views initialized successfully");
            }
        }

        // Always create test VR textures for debugging when using Vulkan backend
        if config.renderer_config.graphics_backend == GraphicsBackend::Vulkan {
            if let Err(e) = vr_renderer.create_test_vr_textures(renderer.device()) {
                log_warning!("Failed to create test VR textures: {}", e);
            } else {
                log_info!("Test VR textures created for VR rendering debugging");
            }
        }

        // Create VR window manager
        let mut vr_window_manager = VrWindowManager::new();

        // Create VR test windows if we have test VR textures
        if vr_renderer.has_render_targets() {
            if let Err(e) = vr_window_manager.create_vr_test_windows(event_loop).await {
                log_warning!("Failed to create VR test windows: {}", e);
            } else {
                log_info!("Created VR test windows for debugging");
            }
        }

        Ok(Self {
            window,
            renderer,
            input_manager,
            keybind_manager: KeybindManager::new(),
            time_manager,
            running: true,
            current_fullscreen_mode: config.fullscreen_mode,
            profiler,
            debug_overlay,
            mouse_grabbed: false,
            vr_renderer,
            vr_window_manager,
        })
    }

    pub async fn new_with_renderer_config(event_loop: &EventLoop<()>, renderer_config: RendererConfig) -> anyhow::Result<Self> {
        let config = EngineConfig {
            renderer_config,
            ..Default::default()
        };
        Self::new_with_config(event_loop, config).await
    }

    pub fn handle_event(&mut self, event: &Event<()>) {
        match event {
            Event::WindowEvent { event, window_id } => {
                // Check if this is a VR window event
                if self.vr_window_manager.is_vr_window(*window_id) {
                    match event {
                        WindowEvent::CloseRequested => {
                            // Don't close the main application when VR windows are closed
                            log_info!("VR window close requested");
                        }
                        WindowEvent::Resized(physical_size) => {
                            self.vr_window_manager.handle_resize(*window_id, *physical_size);
                        }
                        WindowEvent::RedrawRequested => {
                            self.render_vr_windows();
                        }
                        _ => {}
                    }
                } else {
                    // Main window event
                    self.profiler.begin("input_handling");
                    self.input_manager.handle_window_event(event);
                    self.keybind_manager.handle_window_event(event);
                    self.profiler.end("input_handling");

                    match event {
                        WindowEvent::CloseRequested => {
                            self.running = false;
                        }
                        WindowEvent::Resized(physical_size) => {
                            self.renderer.request_resize(*physical_size);
                        }
                        WindowEvent::ScaleFactorChanged { .. } => {}
                        WindowEvent::RedrawRequested => {
                            self.profiler.begin_frame();
                            self.update();
                            self.render();
                            self.profiler.end_frame();

                            // Update debug overlay
                            self.debug_overlay.update(&self.profiler, &self.renderer);
                        }
                        _ => {}
                    }
                }
            }
            Event::DeviceEvent { event, .. } => {
                // Handle raw device input (important for mouse when cursor is grabbed)
                self.profiler.begin("input_handling");
                self.input_manager.handle_device_event(event);
                self.profiler.end("input_handling");
            }
            Event::AboutToWait => {
                self.window.request_redraw();
                self.vr_window_manager.request_redraw();
            }
            _ => {}
        }
    }

    pub fn update(&mut self) {
        self.profiler.begin("time_manager_update");
        self.time_manager.update();
        self.profiler.end("time_manager_update");

        self.profiler.begin("input_manager_update");
        self.input_manager.update();
        self.keybind_manager.update();
        self.profiler.end("input_manager_update");
    }

    pub fn render(&mut self) {
        self.renderer.update_resize();

        self.profiler.begin("render");

        // Check if VR multiview rendering is supported and has render targets
        let use_vr_multiview = self.vr_renderer.has_render_targets() && self.renderer.supports_vr_multiview();

        if use_vr_multiview {
            // Update VR system
            if let Err(e) = self.vr_renderer.update() {
                log_error!("VR update error: {:?}", e);
            }

            // Check if we should render to VR
            let should_render_vr = match self.vr_renderer.begin_frame() {
                Ok(should_render) => should_render,
                Err(e) => {
                    log_error!("VR begin frame error: {:?}", e);
                    false
                }
            };

            if should_render_vr {
                // Get VR camera matrices
                let desktop_camera_view_proj = self.renderer.renderer_3d().get_camera_view_proj();

                // Get VR eye matrices (VR camera follows desktop camera position exactly)
                let (left_eye_view_proj, right_eye_view_proj) = if let Some(vr_camera) = self.vr_renderer.camera() {
                    // For now, use the same desktop camera for both eyes
                    // This ensures VR camera follows desktop camera exactly (no HMD movement)
                    // The stereo effect comes from the multiview rendering, not different camera positions
                    let stereo_matrices = vr_camera.stereo_view_projection();
                    (stereo_matrices[0], stereo_matrices[1])
                } else {
                    // Fallback to desktop camera for both eyes
                    (desktop_camera_view_proj, desktop_camera_view_proj)
                };

                // Get VR render targets from VR system
                let vr_targets = self.vr_renderer.get_render_targets();

                // Render with unified multiview pipeline (includes desktop + VR)
                if let Err(e) = self.renderer.render_vr_multiview(
                    desktop_camera_view_proj,
                    left_eye_view_proj,
                    right_eye_view_proj,
                    &vr_targets,
                ) {
                    log_error!("VR multiview render error: {:?}", e);
                }

                if let Err(e) = self.vr_renderer.end_frame() {
                    log_error!("VR end frame error: {:?}", e);
                }
            } else {
                // Render desktop only when VR frame is not ready
                if let Err(e) = self.renderer.render() {
                    log_error!("Desktop render error: {:?}", e);
                }
            }
        } else {
            // Fallback: Use separate VR pipeline or desktop-only rendering
            if self.vr_renderer.is_enabled() {
                // Update VR system
                if let Err(e) = self.vr_renderer.update() {
                    log_error!("VR update error: {:?}", e);
                }

                // Check if we should render to VR
                let should_render_vr = match self.vr_renderer.begin_frame() {
                    Ok(should_render) => should_render,
                    Err(e) => {
                        log_error!("VR begin frame error: {:?}", e);
                        false
                    }
                };

                // Render to desktop window
                if let Err(e) = self.renderer.render() {
                    log_error!("Render error: {:?}", e);
                }

                // Render to VR using separate pipeline (fallback)
                if should_render_vr {
                    println!("WARNING: Using fallback VR rendering - multiview not supported");
                    // Extract all data from desktop renderer in one go
                    let renderer_3d = self.renderer.renderer_3d();
                    let scene_data = renderer_3d.get_scene_data_for_vr();
                    let meshes = renderer_3d.get_all_meshes();
                    let desktop_camera_view_proj = renderer_3d.get_camera_view_proj();

                    // Set desktop camera data for VR synchronization (so VR can follow WASD movement)
                    self.vr_renderer.set_desktop_camera_data(desktop_camera_view_proj);

                    // Always pass scene data to VR (even if empty) so VR can render
                    self.vr_renderer.set_scene_data_with_meshes(scene_data, meshes);

                    if let Err(e) = self.vr_renderer.end_frame() {
                        log_error!("VR end frame error: {:?}", e);
                    }
                }
            } else {
                // Desktop-only rendering
                if let Err(e) = self.renderer.render() {
                    log_error!("Render error: {:?}", e);
                }
            }
        }

        self.profiler.end("render");

        // Also render to VR windows if they exist
        if self.vr_window_manager.has_vr_windows() {
            self.render_vr_windows();
        }
    }

    /// Render VR eye views to separate windows for testing
    fn render_vr_windows(&mut self) {
        // Update VR window resize
        self.vr_window_manager.update_resize();

        // Get VR render targets
        let vr_targets = self.vr_renderer.get_render_targets();

        if vr_targets.len() >= 2 {
            // Render left eye
            if let Err(e) = self.vr_window_manager.render_vr_eye(VrEye::Left, vr_targets[0]) {
                log_error!("Failed to render left VR eye: {:?}", e);
            }

            // Render right eye
            if let Err(e) = self.vr_window_manager.render_vr_eye(VrEye::Right, vr_targets[1]) {
                log_error!("Failed to render right VR eye: {:?}", e);
            }
        }
    }

    /// Sets VSync mode for the renderer
    pub fn set_vsync_mode(&mut self, vsync_mode: crate::graphics::VSyncMode) {
        self.renderer.set_vsync_mode(vsync_mode);
    }

    /// Gets the current VSync mode
    pub fn vsync_mode(&self) -> crate::graphics::VSyncMode {
        self.renderer.vsync_mode()
    }

    /// Gets the current graphics backend
    pub fn graphics_backend(&self) -> GraphicsBackend {
        self.renderer.graphics_backend()
    }

    /// Gets available graphics backends for the current platform
    pub fn available_backends(&self) -> Vec<GraphicsBackend> {
        self.renderer.available_backends()
    }

    /// Check if VR is enabled and initialized
    pub fn is_vr_enabled(&self) -> bool {
        self.vr_renderer.is_enabled()
    }

    /// Check if VR session is currently running
    pub fn is_vr_session_running(&self) -> bool {
        self.vr_renderer.is_session_running()
    }

    /// Get VR camera for head tracking information
    pub fn vr_camera(&self) -> Option<&crate::vr::VrCamera> {
        self.vr_renderer.camera()
    }

    /// Get VR input for controller tracking and input
    pub fn vr_input(&self) -> Option<&crate::vr::VrInput> {
        self.vr_renderer.input()
    }

    /// Enables or disables CPU frame ahead rendering
    pub fn set_cpu_frame_ahead_enabled(&mut self, enabled: bool) {
        self.renderer.set_cpu_frame_ahead_enabled(enabled);
    }

    /// Checks if CPU frame ahead rendering is enabled
    pub fn is_cpu_frame_ahead_enabled(&self) -> bool {
        self.renderer.is_cpu_frame_ahead_enabled()
    }

    /// Sets CPU frame render ahead setting
    pub fn set_cpu_frame_ahead(&mut self, cpu_frame_ahead: crate::graphics::CpuFrameAhead) {
        self.renderer.set_cpu_frame_ahead(cpu_frame_ahead);
    }

    /// Gets the current CPU frame render ahead setting
    pub fn cpu_frame_ahead(&self) -> crate::graphics::CpuFrameAhead {
        self.renderer.cpu_frame_ahead()
    }

    /// Binds an action to a key
    pub fn bind_action(&mut self, action: &str, key: winit::keyboard::KeyCode) {
        self.keybind_manager.bind_action(action, key);
    }

    /// Replaces all bindings for an action with a single key
    pub fn rebind_action(&mut self, action: &str, key: winit::keyboard::KeyCode) {
        self.keybind_manager.rebind_action(action, key);
    }

    /// Checks if an action is currently pressed
    pub fn is_action_pressed(&self, action: &str) -> bool {
        self.keybind_manager.is_action_pressed(action)
    }

    /// Gets all keys bound to an action
    pub fn get_action_keys(&self, action: &str) -> Vec<winit::keyboard::KeyCode> {
        self.keybind_manager.get_action_keys(action)
    }



    /// Sets fullscreen mode
    pub fn set_fullscreen_mode(&mut self, fullscreen_mode: FullscreenMode) {
        if self.current_fullscreen_mode != fullscreen_mode {
            self.current_fullscreen_mode = fullscreen_mode.clone();

            let primary_monitor = self.window.primary_monitor()
                .or_else(|| self.window.available_monitors().next());

            let fullscreen = fullscreen_mode.to_winit_fullscreen(primary_monitor);
            self.window.set_fullscreen(fullscreen);
        }
    }

    /// Gets the current fullscreen mode
    pub fn fullscreen_mode(&self) -> &FullscreenMode {
        &self.current_fullscreen_mode
    }

    /// Toggles between windowed and borderless fullscreen
    pub fn toggle_fullscreen(&mut self) {
        match self.current_fullscreen_mode {
            FullscreenMode::Windowed => {
                self.set_fullscreen_mode(FullscreenMode::BorderlessFullscreen);
            }
            _ => {
                self.set_fullscreen_mode(FullscreenMode::Windowed);
            }
        }
    }

    /// Gets available video modes for exclusive fullscreen
    pub fn available_video_modes(&self) -> Vec<VideoMode> {
        self.window.primary_monitor()
            .or_else(|| self.window.available_monitors().next())
            .map(|monitor| monitor.video_modes().collect())
            .unwrap_or_default()
    }

    /// Sets exclusive fullscreen with the best available video mode
    pub fn set_exclusive_fullscreen_auto(&mut self) {
        self.set_fullscreen_mode(FullscreenMode::ExclusiveFullscreenAuto);
    }

    /// Sets exclusive fullscreen with a specific video mode
    pub fn set_exclusive_fullscreen(&mut self, video_mode: VideoMode) {
        self.set_fullscreen_mode(FullscreenMode::ExclusiveFullscreen(video_mode));
    }

    /// Gets debug overlay text for display
    pub fn debug_text(&self) -> &str {
        self.debug_overlay.get_text()
    }

    /// Toggles debug overlay visibility
    pub fn toggle_debug_overlay(&mut self) {
        self.debug_overlay.toggle();
    }

    /// Enables/disables debug overlay
    pub fn set_debug_overlay_enabled(&mut self, enabled: bool) {
        self.debug_overlay.set_enabled(enabled);
    }

    /// Checks if debug overlay is enabled
    pub fn is_debug_overlay_enabled(&self) -> bool {
        self.debug_overlay.is_enabled()
    }

    /// Enables/disables profiler
    pub fn set_profiler_enabled(&mut self, enabled: bool) {
        self.profiler.set_enabled(enabled);
    }

    /// Checks if profiler is enabled
    pub fn is_profiler_enabled(&self) -> bool {
        self.profiler.is_enabled()
    }

    /// Gets profiler reference for custom profiling
    pub fn profiler(&mut self) -> &mut Profiler {
        &mut self.profiler
    }

    /// Gets mutable access to the 3D renderer for immediate mode rendering
    pub fn renderer_3d(&mut self) -> &mut crate::graphics::Renderer3D {
        self.renderer.renderer_3d()
    }

    /// Loads a mesh into the 3D renderer and returns its ID
    pub fn load_mesh(&mut self, mesh: crate::graphics::Mesh) -> u64 {
        self.renderer.load_mesh(mesh)
    }

    /// Loads a mesh with VRAM tracking into the 3D renderer and returns its ID
    pub fn load_mesh_with_vram_tracking(&mut self, mut mesh: crate::graphics::Mesh) -> u64 {
        mesh.upload_to_gpu_with_profiler(self.renderer.device(), Some(&mut self.profiler));
        self.renderer.renderer_3d().load_uploaded_mesh(mesh)
    }

    /// Enables or disables background clearing
    pub fn set_clear_background(&mut self, clear: bool) {
        self.renderer.set_clear_background(clear);
    }

    /// Checks if background clearing is enabled
    pub fn is_clear_background(&self) -> bool {
        self.renderer.is_clear_background()
    }

    /// Sets the background clear color
    pub fn set_background_color(&mut self, color: [f32; 4]) {
        self.renderer.set_background_color(wgpu::Color {
            r: color[0] as f64,
            g: color[1] as f64,
            b: color[2] as f64,
            a: color[3] as f64,
        });
    }

    /// Enables or disables depth buffer clearing
    pub fn set_clear_depth(&mut self, clear: bool) {
        self.renderer.renderer_3d().set_clear_depth(clear);
    }

    /// Checks if depth buffer clearing is enabled
    pub fn is_clear_depth(&self) -> bool {
        false // TODO: Fix this by storing the state in Engine or Renderer
    }

    /// Switches to a different shader program
    pub fn switch_shader_program(&mut self, program_name: &str) {
        self.renderer.switch_shader_program(program_name);
    }

    /// Checks for shader file changes and hot-reloads if needed
    pub fn check_shader_updates(&mut self) {
        self.renderer.check_shader_updates();
    }

    /// Toggles face culling for debugging
    pub fn toggle_face_culling(&mut self) {
        self.renderer.toggle_face_culling();
    }

    /// Create a compute job
    pub fn create_compute_job<T: bytemuck::Pod + bytemuck::Zeroable>(
        &mut self,
        job_name: &str,
        shader_name: &str,
        shader_filename: &str,
        storage_data: &[T],
        uniform_data: &[u8],
        workgroup_size: (u32, u32, u32),
    ) -> Result<(), String> {
        self.renderer.create_compute_job(
            job_name,
            shader_name,
            shader_filename,
            storage_data,
            uniform_data,
            workgroup_size,
        )
    }

    /// Execute a compute job
    pub fn execute_compute_job(&self, job_name: &str) -> Result<(), String> {
        self.renderer.execute_compute_job(job_name)
    }

    /// Update uniform data for a compute job
    pub fn update_compute_uniform_data(&self, job_name: &str, data: &[u8]) -> Result<(), String> {
        self.renderer.compute_manager_ref().update_uniform_data(job_name, data)
    }

    /// Read storage data from a compute job
    pub fn read_compute_storage_data<T: bytemuck::Pod + bytemuck::Zeroable + Clone>(&self, job_name: &str) -> Result<Vec<T>, String> {
        self.renderer.compute_manager_ref().read_storage_data(job_name)
    }

    /// List all compute jobs
    pub fn list_compute_jobs(&self) -> Vec<&str> {
        self.renderer.compute_manager_ref().list_jobs()
    }

    // Mouse input methods

    /// Checks if a mouse button is currently pressed
    pub fn is_mouse_button_pressed(&self, button: MouseButton) -> bool {
        self.input_manager.is_mouse_button_pressed(button)
    }

    /// Checks if a mouse button was just pressed this frame
    pub fn is_mouse_button_just_pressed(&self, button: MouseButton) -> bool {
        self.input_manager.is_mouse_button_just_pressed(button)
    }

    /// Checks if a mouse button was just released this frame
    pub fn is_mouse_button_just_released(&self, button: MouseButton) -> bool {
        self.input_manager.is_mouse_button_just_released(button)
    }

    /// Gets the current mouse position
    pub fn mouse_position(&self) -> (f64, f64) {
        self.input_manager.mouse_position()
    }

    /// Gets the mouse movement delta since last frame
    pub fn mouse_delta(&mut self) -> (f64, f64) {
        self.input_manager.mouse_delta()
    }

    /// Grabs the mouse cursor (confines it to the window and hides it)
    pub fn grab_mouse(&mut self) -> Result<(), String> {
        if !self.mouse_grabbed {
            self.window.set_cursor_visible(false);
            match self.window.set_cursor_grab(CursorGrabMode::Confined) {
                Ok(()) => {
                    self.mouse_grabbed = true;
                    self.input_manager.set_mouse_grabbed(true);
                    Ok(())
                }
                Err(e) => {
                    // Try locked mode as fallback
                    match self.window.set_cursor_grab(CursorGrabMode::Locked) {
                        Ok(()) => {
                            self.mouse_grabbed = true;
                            self.input_manager.set_mouse_grabbed(true);
                            Ok(())
                        }
                        Err(_) => {
                            self.window.set_cursor_visible(true);
                            Err(format!("Failed to grab mouse cursor: {}", e))
                        }
                    }
                }
            }
        } else {
            Ok(())
        }
    }

    /// Ungrabs the mouse cursor (allows it to move freely and shows it)
    pub fn ungrab_mouse(&mut self) -> Result<(), String> {
        if self.mouse_grabbed {
            self.window.set_cursor_visible(true);
            match self.window.set_cursor_grab(CursorGrabMode::None) {
                Ok(()) => {
                    self.mouse_grabbed = false;
                    self.input_manager.set_mouse_grabbed(false);
                    Ok(())
                }
                Err(e) => Err(format!("Failed to ungrab mouse cursor: {}", e))
            }
        } else {
            Ok(())
        }
    }

    /// Checks if the mouse cursor is currently grabbed
    pub fn is_mouse_grabbed(&self) -> bool {
        self.mouse_grabbed
    }

    /// Resets the mouse cursor to the center of the window
    /// This should be called every frame when mouse is grabbed to prevent cursor hitting window boundaries
    /// Since we use raw mouse motion when grabbed, the cursor position reset won't affect mouse delta
    pub fn reset_mouse_to_center(&mut self) -> Result<(), String> {
        if self.mouse_grabbed {
            let window_size = self.window.inner_size();
            let center_x = window_size.width as f64 / 2.0;
            let center_y = window_size.height as f64 / 2.0;

            // Set cursor position to center
            // This won't affect mouse delta since we ignore CursorMoved events when grabbed
            match self.window.set_cursor_position(winit::dpi::PhysicalPosition::new(center_x, center_y)) {
                Ok(()) => Ok(()),
                Err(e) => Err(format!("Failed to reset mouse position: {}", e))
            }
        } else {
            Ok(())
        }
    }




}
