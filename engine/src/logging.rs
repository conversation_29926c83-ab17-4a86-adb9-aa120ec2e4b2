use std::sync::Mutex;
use std::fmt;


#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, PartialOrd, Ord)]
pub enum LogLevel {
    Debug = 0,
    Info = 1,
    Warning = 2,
    Error = 3,
}

impl fmt::Display for LogLevel {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            LogLevel::Debug => write!(f, "DEBUG"),
            LogLevel::Info => write!(f, "INFO"),
            LogLevel::Warning => write!(f, "WARN"),
            LogLevel::Error => write!(f, "ERROR"),
        }
    }
}

impl LogLevel {

    pub fn color_code(&self) -> &'static str {
        match self {
            LogLevel::Debug => "\x1b[36m",    // Cyan
            <PERSON>l::Info => "\x1b[32m",     // Green
            LogLevel::Warning => "\x1b[33m",  // Yellow
            LogLevel::Error => "\x1b[31m",    // Red
        }
    }
}


static LOGGER: Mutex<Logger> = Mutex::new(Logger::new());


pub struct Logger {
    filter_level: LogLevel,
    enabled: bool,
}

impl Logger {

    const fn new() -> Self {
        Self {
            filter_level: LogLevel::Info, // Default to Info level
            enabled: true,
        }
    }


    pub fn set_filter(&mut self, level: LogLevel) {
        self.filter_level = level;
    }


    pub fn get_filter(&self) -> LogLevel {
        self.filter_level
    }


    pub fn set_enabled(&mut self, enabled: bool) {
        self.enabled = enabled;
    }


    pub fn should_log(&self, level: LogLevel) -> bool {
        self.enabled && level >= self.filter_level
    }


    fn log_internal(&self, level: LogLevel, message: &str) {
        if !self.should_log(level) {
            return;
        }

        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_millis();

        // Format: [TIMESTAMP] LEVEL: MESSAGE
        println!("{}[{}] {}: {}\x1b[0m",
                 level.color_code(),
                 timestamp % 100000, // Last 5 digits for brevity
                 level,
                 message);
    }
}


pub fn log(level: LogLevel, message: &str) {
    if let Ok(logger) = LOGGER.lock() {
        logger.log_internal(level, message);
    }
}


pub fn set_log_filtering(level: LogLevel) {
    if let Ok(mut logger) = LOGGER.lock() {
        logger.set_filter(level);
        log(LogLevel::Info, &format!("Log filter set to: {}", level));
    }
}


pub fn get_log_filtering() -> LogLevel {
    LOGGER.lock()
        .map(|logger| logger.get_filter())
        .unwrap_or(LogLevel::Info)
}


pub fn set_logging_enabled(enabled: bool) {
    if let Ok(mut logger) = LOGGER.lock() {
        logger.set_enabled(enabled);
    }
}


#[macro_export]
macro_rules! log_error {
    ($($arg:tt)*) => {
        $crate::logging::log($crate::logging::LogLevel::Error, &format!($($arg)*))
    };
}

#[macro_export]
macro_rules! log_warning {
    ($($arg:tt)*) => {
        $crate::logging::log($crate::logging::LogLevel::Warning, &format!($($arg)*))
    };
}

#[macro_export]
macro_rules! log_info {
    ($($arg:tt)*) => {
        $crate::logging::log($crate::logging::LogLevel::Info, &format!($($arg)*))
    };
}

#[macro_export]
macro_rules! log_debug {
    ($($arg:tt)*) => {
        $crate::logging::log($crate::logging::LogLevel::Debug, &format!($($arg)*))
    };
}




#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_log_levels() {
        assert!(LogLevel::Error > LogLevel::Warning);
        assert!(LogLevel::Warning > LogLevel::Info);
        assert!(LogLevel::Info > LogLevel::Debug);
    }

    #[test]
    fn test_filtering() {
        set_log_filtering(LogLevel::Warning);
        assert_eq!(get_log_filtering(), LogLevel::Warning);

        // Test that the logger respects filtering
        let logger = Logger::new();
        assert!(!logger.should_log(LogLevel::Debug));
        assert!(!logger.should_log(LogLevel::Info));
    }

    #[test]
    fn test_log_display() {
        assert_eq!(format!("{}", LogLevel::Debug), "DEBUG");
        assert_eq!(format!("{}", LogLevel::Info), "INFO");
        assert_eq!(format!("{}", LogLevel::Warning), "WARN");
        assert_eq!(format!("{}", LogLevel::Error), "ERROR");
    }
}
