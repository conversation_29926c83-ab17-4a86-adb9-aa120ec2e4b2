use std::time::{Duration, Instant};

pub struct TimeManager {
    start_time: Instant,
    last_frame_time: Instant,
    delta_time: Duration,
    total_time: Duration,
    frame_count: u64,
    fps: f32,
    fps_timer: Duration,
    fps_history: Vec<f32>,
    max_fps_history: usize,
}

impl TimeManager {
    pub fn new() -> Self {
        let now = Instant::now();
        Self {
            start_time: now,
            last_frame_time: now,
            delta_time: Duration::ZERO,
            total_time: Duration::ZERO,
            frame_count: 0,
            fps: 0.0,
            fps_timer: Duration::ZERO,
            fps_history: Vec::new(),
            max_fps_history: 60, // Keep 1 second of history at ~60 FPS
        }
    }

    pub fn update(&mut self) {
        let now = Instant::now();
        self.delta_time = now - self.last_frame_time;
        self.last_frame_time = now;
        self.total_time = now - self.start_time;
        self.frame_count += 1;



        // Update FPS every second (for compatibility)
        self.fps_timer += self.delta_time;
        if self.fps_timer >= Duration::from_secs(1) {
            self.fps = self.frame_count as f32 / self.fps_timer.as_secs_f32();
            self.frame_count = 0;
            self.fps_timer = Duration::ZERO;
        }
    }

    pub fn delta_time(&self) -> Duration {
        self.delta_time
    }

    pub fn delta_time_f32(&self) -> f32 {
        self.delta_time.as_secs_f32()
    }

    pub fn total_time(&self) -> Duration {
        self.total_time
    }

    pub fn total_time_f32(&self) -> f32 {
        self.total_time.as_secs_f32()
    }

    pub fn fps(&self) -> f32 {
        self.fps
    }

    /// Get instantaneous FPS (calculated from current frame's delta time)
    pub fn instantaneous_fps(&self) -> f32 {
        if self.delta_time.as_secs_f32() > 0.0 {
            1.0 / self.delta_time.as_secs_f32()
        } else {
            0.0
        }
    }

    /// Get average FPS from the last 1 second (rolling average)
    pub fn average_fps(&self) -> f32 {
        if !self.fps_history.is_empty() {
            self.fps_history.iter().sum::<f32>() / self.fps_history.len() as f32
        } else {
            0.0
        }
    }

    /// Add an FPS value to the rolling average (to be called with profiler FPS)
    pub fn add_fps_to_history(&mut self, fps: f32) {
        self.fps_history.push(fps);
        if self.fps_history.len() > self.max_fps_history {
            self.fps_history.remove(0);
        }
    }

    pub fn frame_count(&self) -> u64 {
        self.frame_count
    }
}

impl Default for TimeManager {
    fn default() -> Self {
        Self::new()
    }
}
