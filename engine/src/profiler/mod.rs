use std::collections::HashMap;
use std::time::{Duration, Instant};


#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct ProfileMeasurement {
    pub name: String,
    pub duration: Duration,
    pub start_time: Instant,
    pub end_time: Instant,
}


#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct ProfileStats {
    pub name: String,
    pub total_time: Duration,
    pub average_time: Duration,
    pub min_time: Duration,
    pub max_time: Duration,
    pub sample_count: u64,
    pub percentage_of_frame: f32,
}


#[derive(Debug, <PERSON><PERSON>)]
pub struct VramUsage {
    pub buffers: u64,      // Bytes used by buffers
    pub textures: u64,     // Bytes used by textures
    pub total: u64,        // Total VRAM usage
}

impl VramUsage {
    pub fn new() -> Self {
        Self {
            buffers: 0,
            textures: 0,
            total: 0,
        }
    }

    pub fn update_total(&mut self) {
        self.total = self.buffers + self.textures;
    }
}


pub struct Profiler {
    measurements: Vec<ProfileMeasurement>,
    active_timers: HashMap<String, Instant>,
    frame_start: Instant,
    frame_duration: Duration,
    stats_history: HashMap<String, Vec<Duration>>,
    max_history_size: usize,
    enabled: bool,
    vram_usage: VramUsage,
}

impl Profiler {
    pub fn new() -> Self {
        Self {
            measurements: Vec::new(),
            active_timers: HashMap::new(),
            frame_start: Instant::now(),
            frame_duration: Duration::ZERO,
            stats_history: HashMap::new(),
            max_history_size: 60, // Keep 60 frames of history (1 second at 60fps)
            enabled: true,
            vram_usage: VramUsage::new(),
        }
    }


    pub fn begin(&mut self, name: &str) {
        if !self.enabled {
            return;
        }
        self.active_timers.insert(name.to_string(), Instant::now());
    }


    pub fn end(&mut self, name: &str) {
        if !self.enabled {
            return;
        }

        let end_time = Instant::now();
        if let Some(start_time) = self.active_timers.remove(name) {
            let duration = end_time - start_time;

            self.measurements.push(ProfileMeasurement {
                name: name.to_string(),
                duration,
                start_time,
                end_time,
            });

            // Add to history
            let history = self.stats_history.entry(name.to_string()).or_insert_with(Vec::new);
            history.push(duration);
            if history.len() > self.max_history_size {
                history.remove(0);
            }
        }
    }


    pub fn begin_frame(&mut self) {
        if !self.enabled {
            return;
        }
        self.frame_start = Instant::now();
        self.measurements.clear();
    }


    pub fn end_frame(&mut self) {
        if !self.enabled {
            return;
        }
        self.frame_duration = Instant::now() - self.frame_start;
    }

    /// Get statistics for all profiled sections
    pub fn get_stats(&self) -> Vec<ProfileStats> {
        if !self.enabled {
            return Vec::new();
        }

        let mut stats = Vec::new();
        let frame_time_ms = self.frame_duration.as_secs_f32() * 1000.0;

        // Get current frame measurements to calculate accurate percentages
        let mut current_frame_times: std::collections::HashMap<String, Duration> = std::collections::HashMap::new();
        for measurement in &self.measurements {
            *current_frame_times.entry(measurement.name.clone()).or_insert(Duration::ZERO) += measurement.duration;
        }

        for (name, history) in &self.stats_history {
            if history.is_empty() {
                continue;
            }

            let total_time: Duration = history.iter().sum();
            let sample_count = history.len() as u64;
            let average_time = total_time / sample_count as u32;
            let min_time = *history.iter().min().unwrap();
            let max_time = *history.iter().max().unwrap();

            // Calculate percentage based on current frame measurements, not history
            let current_frame_time = current_frame_times.get(name).unwrap_or(&Duration::ZERO);
            let percentage_of_frame = if frame_time_ms > 0.0 {
                (current_frame_time.as_secs_f32() * 1000.0 / frame_time_ms) * 100.0
            } else {
                0.0
            };

            // Only include stats that have measurements in recent frames or current frame
            let is_recent = current_frame_time > &Duration::ZERO ||
                           history.iter().rev().take(5).any(|&d| d > Duration::ZERO);

            if is_recent {
                stats.push(ProfileStats {
                    name: name.clone(),
                    total_time,
                    average_time,
                    min_time,
                    max_time,
                    sample_count,
                    percentage_of_frame,
                });
            }
        }

        // Sort by percentage of frame time (highest first)
        stats.sort_by(|a, b| b.percentage_of_frame.partial_cmp(&a.percentage_of_frame).unwrap());
        stats
    }

    /// Get current frame duration
    pub fn frame_duration(&self) -> Duration {
        self.frame_duration
    }

    /// Get current FPS
    pub fn fps(&self) -> f32 {
        if self.frame_duration.as_secs_f32() > 0.0 {
            1.0 / self.frame_duration.as_secs_f32()
        } else {
            0.0
        }
    }

    /// Enable or disable profiling
    pub fn set_enabled(&mut self, enabled: bool) {
        self.enabled = enabled;
        if !enabled {
            self.measurements.clear();
            self.active_timers.clear();
            self.stats_history.clear();
        }
    }

    /// Check if profiling is enabled
    pub fn is_enabled(&self) -> bool {
        self.enabled
    }

    /// Get memory usage estimate (in bytes)
    pub fn memory_usage(&self) -> usize {
        let measurements_size = self.measurements.len() * std::mem::size_of::<ProfileMeasurement>();
        let timers_size = self.active_timers.len() * (std::mem::size_of::<String>() + std::mem::size_of::<Instant>());
        let history_size: usize = self.stats_history.iter()
            .map(|(k, v)| k.len() + v.len() * std::mem::size_of::<Duration>())
            .sum();

        measurements_size + timers_size + history_size
    }

    /// Add buffer memory usage to VRAM tracking
    pub fn add_buffer_memory(&mut self, bytes: u64) {
        self.vram_usage.buffers += bytes;
        self.vram_usage.update_total();
    }

    /// Remove buffer memory usage from VRAM tracking
    pub fn remove_buffer_memory(&mut self, bytes: u64) {
        self.vram_usage.buffers = self.vram_usage.buffers.saturating_sub(bytes);
        self.vram_usage.update_total();
    }

    /// Add texture memory usage to VRAM tracking
    pub fn add_texture_memory(&mut self, bytes: u64) {
        self.vram_usage.textures += bytes;
        self.vram_usage.update_total();
    }

    /// Remove texture memory usage from VRAM tracking
    pub fn remove_texture_memory(&mut self, bytes: u64) {
        self.vram_usage.textures = self.vram_usage.textures.saturating_sub(bytes);
        self.vram_usage.update_total();
    }

    /// Get current VRAM usage
    pub fn vram_usage(&self) -> &VramUsage {
        &self.vram_usage
    }

    /// Reset VRAM usage tracking (useful for debugging)
    pub fn reset_vram_tracking(&mut self) {
        self.vram_usage = VramUsage::new();
    }
}

impl Default for Profiler {
    fn default() -> Self {
        Self::new()
    }
}

/// RAII helper for automatic profiling
pub struct ProfileScope<'a> {
    profiler: &'a mut Profiler,
    name: String,
}

impl<'a> ProfileScope<'a> {
    pub fn new(profiler: &'a mut Profiler, name: &str) -> Self {
        profiler.begin(name);
        Self {
            profiler,
            name: name.to_string(),
        }
    }
}

impl<'a> Drop for ProfileScope<'a> {
    fn drop(&mut self) {
        self.profiler.end(&self.name);
    }
}

/// Macro for easy profiling
#[macro_export]
macro_rules! profile {
    ($profiler:expr, $name:expr, $code:block) => {
        {
            $profiler.begin($name);
            let result = $code;
            $profiler.end($name);
            result
        }
    };
}

/// Macro for RAII profiling scope
#[macro_export]
macro_rules! profile_scope {
    ($profiler:expr, $name:expr) => {
        let _scope = $crate::profiler::ProfileScope::new($profiler, $name);
    };
}
