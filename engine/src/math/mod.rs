pub mod transform;

pub use transform::*;

// Re-export glam types for convenience
pub use glam::{
    Mat3, Mat4, Quat, Vec2, Vec3, Vec4,
    IVec2, IVec3, IVec4,
    UVec2, UVec3, UVec4,
    DVec2, DVec3, DVec4,
};

/// Common mathematical constants
pub mod constants {
    pub const PI: f32 = std::f32::consts::PI;
    pub const TAU: f32 = std::f32::consts::TAU;
    pub const E: f32 = std::f32::consts::E;
    pub const SQRT_2: f32 = std::f32::consts::SQRT_2;
}

/// Utility functions
pub fn lerp(a: f32, b: f32, t: f32) -> f32 {
    a + (b - a) * t
}

pub fn clamp(value: f32, min: f32, max: f32) -> f32 {
    value.max(min).min(max)
}

pub fn degrees_to_radians(degrees: f32) -> f32 {
    degrees * constants::PI / 180.0
}

pub fn radians_to_degrees(radians: f32) -> f32 {
    radians * 180.0 / constants::PI
}
