use winit::event::{ElementState, <PERSON><PERSON><PERSON><PERSON>, WindowEvent, <PERSON><PERSON><PERSON><PERSON>};
use winit::keyboard::{<PERSON><PERSON><PERSON>, PhysicalKey};
use std::collections::HashSet;

// Re-export keybind manager
pub mod keybind_manager;
pub use keybind_manager::KeybindManager;

pub struct InputManager {
    keys_pressed: HashSet<KeyCode>,
    keys_just_pressed: HashSet<KeyCode>,
    keys_just_released: HashSet<KeyCode>,
    mouse_buttons_pressed: HashSet<MouseButton>,
    mouse_buttons_just_pressed: HashSet<MouseButton>,
    mouse_buttons_just_released: HashSet<MouseButton>,
    mouse_position: (f64, f64),
    mouse_delta: (f64, f64),
    /// Track if mouse is currently grabbed (to use raw motion instead of cursor events)
    mouse_grabbed: bool,
}

impl InputManager {
    pub fn new() -> Self {
        Self {
            keys_pressed: HashSet::new(),
            keys_just_pressed: HashSet::new(),
            keys_just_released: HashSet::new(),
            mouse_buttons_pressed: HashSet::new(),
            mouse_buttons_just_pressed: HashSet::new(),
            mouse_buttons_just_released: HashSet::new(),
            mouse_position: (0.0, 0.0),
            mouse_delta: (0.0, 0.0),
            mouse_grabbed: false,
        }
    }

    pub fn handle_window_event(&mut self, event: &WindowEvent) {
        match event {
            WindowEvent::KeyboardInput {
                event,
                ..
            } => {
                if let PhysicalKey::Code(keycode) = event.physical_key {
                    match event.state {
                        ElementState::Pressed => {
                            if !self.keys_pressed.contains(&keycode) {
                                self.keys_just_pressed.insert(keycode);
                            }
                            self.keys_pressed.insert(keycode);
                        }
                        ElementState::Released => {
                            self.keys_pressed.remove(&keycode);
                            self.keys_just_released.insert(keycode);
                        }
                    }
                }
            }
            WindowEvent::MouseInput { state, button, .. } => {
                match state {
                    ElementState::Pressed => {
                        if !self.mouse_buttons_pressed.contains(button) {
                            self.mouse_buttons_just_pressed.insert(*button);
                        }
                        self.mouse_buttons_pressed.insert(*button);
                    }
                    ElementState::Released => {
                        self.mouse_buttons_pressed.remove(button);
                        self.mouse_buttons_just_released.insert(*button);
                    }
                }
            }
            WindowEvent::CursorMoved { position, .. } => {
                let new_position = (position.x, position.y);

                // Only process cursor movement for delta calculation when mouse is NOT grabbed
                // When grabbed, we use raw mouse motion from DeviceEvent instead
                if !self.mouse_grabbed {
                    self.mouse_delta = (
                        new_position.0 - self.mouse_position.0,
                        new_position.1 - self.mouse_position.1,
                    );
                }

                // Always update position for reference
                self.mouse_position = new_position;
            }
            _ => {}
        }
    }

    pub fn handle_device_event(&mut self, event: &DeviceEvent) {
        match event {
            DeviceEvent::MouseMotion { delta } => {
                // Accumulate mouse delta for raw mouse input (when cursor is grabbed)
                self.mouse_delta.0 += delta.0;
                self.mouse_delta.1 += delta.1;
            }
            _ => {}
        }
    }

    pub fn update(&mut self) {
        self.keys_just_pressed.clear();
        self.keys_just_released.clear();
        self.mouse_buttons_just_pressed.clear();
        self.mouse_buttons_just_released.clear();
        // Don't clear mouse delta here - it's cleared when read
        // Reset cursor flag is cleared when the corresponding cursor movement is detected
    }

    // Key input methods
    pub fn is_key_pressed(&self, key: KeyCode) -> bool {
        self.keys_pressed.contains(&key)
    }

    pub fn is_key_just_pressed(&self, key: KeyCode) -> bool {
        self.keys_just_pressed.contains(&key)
    }

    pub fn is_key_just_released(&self, key: KeyCode) -> bool {
        self.keys_just_released.contains(&key)
    }

    // Mouse input methods
    pub fn is_mouse_button_pressed(&self, button: MouseButton) -> bool {
        self.mouse_buttons_pressed.contains(&button)
    }

    pub fn is_mouse_button_just_pressed(&self, button: MouseButton) -> bool {
        self.mouse_buttons_just_pressed.contains(&button)
    }

    pub fn is_mouse_button_just_released(&self, button: MouseButton) -> bool {
        self.mouse_buttons_just_released.contains(&button)
    }

    pub fn mouse_position(&self) -> (f64, f64) {
        self.mouse_position
    }

    pub fn mouse_delta(&mut self) -> (f64, f64) {
        let delta = self.mouse_delta;
        self.mouse_delta = (0.0, 0.0); // Clear immediately after reading
        delta
    }

    /// Sets the mouse grabbed state
    /// When grabbed, cursor movement events are ignored and only raw mouse motion is used
    pub fn set_mouse_grabbed(&mut self, grabbed: bool) {
        self.mouse_grabbed = grabbed;
        // Clear any accumulated delta when changing grab state
        if grabbed {
            self.mouse_delta = (0.0, 0.0);
        }
    }


}

impl Default for InputManager {
    fn default() -> Self {
        Self::new()
    }
}
