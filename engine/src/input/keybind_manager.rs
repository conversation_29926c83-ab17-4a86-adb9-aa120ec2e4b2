use std::collections::HashMap;
use winit::keyboard::KeyCode;
use winit::event::{WindowEvent, ElementState};

/// Simple action-based keybind system
/// Maps action names to keys and returns if they're currently pressed
#[derive(Debug)]
pub struct KeybindManager {
    /// Maps action names to their bound keys
    action_bindings: HashMap<String, Vec<KeyCode>>,

    /// Current state of all keys (pressed/released)
    key_states: HashMap<KeyCode, bool>,
}

impl KeybindManager {
    /// Create a new empty keybind manager
    pub fn new() -> Self {
        Self {
            action_bindings: HashMap::new(),
            key_states: HashMap::new(),
        }
    }

    /// Bind an action to a key
    /// Multiple keys can be bound to the same action
    pub fn bind_action(&mut self, action: &str, key: KeyCode) {
        self.action_bindings
            .entry(action.to_string())
            .or_insert_with(Vec::new)
            .push(key);
    }

    /// Replace all bindings for an action with a single key
    pub fn rebind_action(&mut self, action: &str, key: KeyCode) {
        self.action_bindings.insert(action.to_string(), vec![key]);
    }

    /// Add an additional key binding to an existing action
    pub fn add_binding(&mut self, action: &str, key: KeyCode) {
        self.bind_action(action, key);
    }

    /// Remove a specific key binding from an action
    pub fn remove_binding(&mut self, action: &str, key: KeyCode) {
        if let Some(bindings) = self.action_bindings.get_mut(action) {
            bindings.retain(|&k| k != key);
            if bindings.is_empty() {
                self.action_bindings.remove(action);
            }
        }
    }

    /// Clear all bindings for an action
    pub fn clear_action(&mut self, action: &str) {
        self.action_bindings.remove(action);
    }

    /// Check if an action is currently pressed
    /// Returns true if ANY of the keys bound to this action are pressed
    pub fn is_action_pressed(&self, action: &str) -> bool {
        if let Some(keys) = self.action_bindings.get(action) {
            keys.iter().any(|&key| self.is_key_pressed(key))
        } else {
            false
        }
    }

    /// Check if a specific key is currently pressed
    pub fn is_key_pressed(&self, key: KeyCode) -> bool {
        self.key_states.get(&key).copied().unwrap_or(false)
    }

    /// Get all keys bound to an action
    pub fn get_action_keys(&self, action: &str) -> Vec<KeyCode> {
        self.action_bindings.get(action).cloned().unwrap_or_default()
    }

    /// Get all available actions
    pub fn get_all_actions(&self) -> Vec<String> {
        self.action_bindings.keys().cloned().collect()
    }

    /// Handle window events (call this from your event loop)
    pub fn handle_window_event(&mut self, event: &WindowEvent) {
        match event {
            WindowEvent::KeyboardInput { event, .. } => {
                if let winit::keyboard::PhysicalKey::Code(keycode) = event.physical_key {
                    let pressed = event.state == ElementState::Pressed;
                    self.key_states.insert(keycode, pressed);
                }
            }
            _ => {}
        }
    }

    /// Update the keybind manager (call this every frame)
    /// Currently does nothing - kept for future use
    pub fn update(&mut self) {
        // Nothing to do - just checking if keys are pressed
    }


}

impl Default for KeybindManager {
    fn default() -> Self {
        Self::new()
    }
}
