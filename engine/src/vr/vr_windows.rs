use winit::{
    event_loop::EventLoop,
    window::{Window, WindowBuilder},
    dpi::LogicalSize,
};
use std::sync::Arc;
use std::collections::HashMap;
use crate::graphics::Renderer;

/// VR window manager for creating test windows to display VR eye views
pub struct VrWindowManager {
    /// Left eye window and renderer
    pub left_eye_window: Option<Arc<Window>>,
    pub left_eye_renderer: Option<Renderer>,
    
    /// Right eye window and renderer
    pub right_eye_window: Option<Arc<Window>>,
    pub right_eye_renderer: Option<Renderer>,
    
    /// Window IDs for event handling
    window_ids: HashMap<winit::window::WindowId, VrEye>,
}

#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq)]
pub enum VrEye {
    Left,
    Right,
}

impl VrWindowManager {
    pub fn new() -> Self {
        Self {
            left_eye_window: None,
            left_eye_renderer: None,
            right_eye_window: None,
            right_eye_renderer: None,
            window_ids: HashMap::new(),
        }
    }

    /// Create VR test windows for left and right eye views
    pub async fn create_vr_test_windows(&mut self, event_loop: &EventLoop<()>) -> anyhow::Result<()> {
        // Create left eye window
        let left_window = Arc::new(WindowBuilder::new()
            .with_title("VR Left Eye")
            .with_inner_size(LogicalSize::new(800, 600))
            .with_position(winit::dpi::PhysicalPosition::new(100, 100))
            .build(event_loop)?);

        // Create right eye window
        let right_window = Arc::new(WindowBuilder::new()
            .with_title("VR Right Eye")
            .with_inner_size(LogicalSize::new(800, 600))
            .with_position(winit::dpi::PhysicalPosition::new(950, 100))
            .build(event_loop)?);

        // Store window IDs for event handling
        self.window_ids.insert(left_window.id(), VrEye::Left);
        self.window_ids.insert(right_window.id(), VrEye::Right);

        // Create renderers for each window
        let left_renderer = Renderer::new(left_window.clone()).await?;
        let right_renderer = Renderer::new(right_window.clone()).await?;

        self.left_eye_window = Some(left_window);
        self.left_eye_renderer = Some(left_renderer);
        self.right_eye_window = Some(right_window);
        self.right_eye_renderer = Some(right_renderer);

        crate::log_info!("Created VR test windows for left and right eye views");
        Ok(())
    }

    /// Check if a window ID belongs to a VR window
    pub fn is_vr_window(&self, window_id: winit::window::WindowId) -> bool {
        self.window_ids.contains_key(&window_id)
    }

    /// Get which VR eye a window represents
    pub fn get_vr_eye(&self, window_id: winit::window::WindowId) -> Option<VrEye> {
        self.window_ids.get(&window_id).copied()
    }

    /// Render VR eye view to the appropriate window
    pub fn render_vr_eye(&mut self, eye: VrEye, vr_texture_view: &wgpu::TextureView) -> anyhow::Result<()> {
        match eye {
            VrEye::Left => {
                if let Some(ref mut renderer) = self.left_eye_renderer {
                    Self::render_texture_to_window_static(renderer, vr_texture_view)?;
                }
            }
            VrEye::Right => {
                if let Some(ref mut renderer) = self.right_eye_renderer {
                    Self::render_texture_to_window_static(renderer, vr_texture_view)?;
                }
            }
        }
        Ok(())
    }

    /// Render a texture to a window using the renderer's render method (static version)
    fn render_texture_to_window_static(renderer: &mut Renderer, _texture_view: &wgpu::TextureView) -> anyhow::Result<()> {
        // For now, just use the renderer's normal render method to show something
        // In a real implementation, we would use a blit shader to copy the VR texture

        match renderer.render() {
            Ok(()) => {
                crate::log_info!("Rendered VR window successfully");
                Ok(())
            }
            Err(e) => {
                Err(anyhow::anyhow!("Failed to render VR window: {:?}", e))
            }
        }
    }

    /// Handle window resize for VR windows
    pub fn handle_resize(&mut self, window_id: winit::window::WindowId, new_size: winit::dpi::PhysicalSize<u32>) {
        if let Some(eye) = self.get_vr_eye(window_id) {
            match eye {
                VrEye::Left => {
                    if let Some(ref mut renderer) = self.left_eye_renderer {
                        renderer.request_resize(new_size);
                    }
                }
                VrEye::Right => {
                    if let Some(ref mut renderer) = self.right_eye_renderer {
                        renderer.request_resize(new_size);
                    }
                }
            }
        }
    }

    /// Update resize for all VR windows
    pub fn update_resize(&mut self) {
        if let Some(ref mut renderer) = self.left_eye_renderer {
            renderer.update_resize();
        }
        if let Some(ref mut renderer) = self.right_eye_renderer {
            renderer.update_resize();
        }
    }

    /// Request redraw for all VR windows
    pub fn request_redraw(&self) {
        if let Some(ref window) = self.left_eye_window {
            window.request_redraw();
        }
        if let Some(ref window) = self.right_eye_window {
            window.request_redraw();
        }
    }

    /// Check if VR windows are created
    pub fn has_vr_windows(&self) -> bool {
        self.left_eye_window.is_some() && self.right_eye_window.is_some()
    }
}

impl Default for VrWindowManager {
    fn default() -> Self {
        Self::new()
    }
}
