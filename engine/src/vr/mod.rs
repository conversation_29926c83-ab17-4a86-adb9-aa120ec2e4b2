//! VR (Virtual Reality) support module
//! 
//! This module provides OpenXR-based VR rendering capabilities for the game engine.
//! VR support is only available when using the Vulkan graphics backend.

#[cfg(feature = "vr")]
pub mod vr_renderer;

#[cfg(feature = "vr")]
pub mod vr_camera;

#[cfg(feature = "vr")]
pub mod vr_input;

#[cfg(feature = "vr")]
pub mod vr_config;

pub mod vr_windows;

// Re-export main VR types when VR feature is enabled
#[cfg(feature = "vr")]
pub use vr_renderer::{VrRenderer, VrSceneData, VrDrawCommand};

#[cfg(feature = "vr")]
pub use vr_camera::VrCamera;

#[cfg(feature = "vr")]
pub use vr_input::VrInput;

#[cfg(feature = "vr")]
pub use vr_config::VrConfig;

pub use vr_windows::{VrWindowManager, VrEye};

// Provide stub types when VR feature is disabled
#[cfg(not(feature = "vr"))]
pub struct VrRenderer;

#[cfg(not(feature = "vr"))]
pub struct VrCamera;

#[cfg(not(feature = "vr"))]
pub struct VrInput;

#[cfg(not(feature = "vr"))]
pub struct VrConfig;

#[cfg(not(feature = "vr"))]
impl VrRenderer {
    pub fn new() -> anyhow::Result<Self> {
        anyhow::bail!("VR support not compiled in. Enable the 'vr' feature to use VR functionality.")
    }
}

#[cfg(not(feature = "vr"))]
impl Default for VrConfig {
    fn default() -> Self {
        Self
    }
}
