//! VR (Virtual Reality) support module
//! 
//! This module provides OpenXR-based VR rendering capabilities for the game engine.
//! VR support is only available when using the Vulkan graphics backend.

#[cfg(feature = "vr")]
pub mod vr_renderer;

#[cfg(feature = "vr")]
pub mod vr_camera;

#[cfg(feature = "vr")]
pub mod vr_input;

#[cfg(feature = "vr")]
pub mod vr_config;

pub mod vr_windows;

pub mod vr_api;

// Re-export main VR types when VR feature is enabled
#[cfg(feature = "vr")]
pub use vr_renderer::{VrRenderer, VrSceneData, VrDrawCommand};

#[cfg(feature = "vr")]
pub use vr_camera::VrCamera;

#[cfg(feature = "vr")]
pub use vr_input::VrInput;

#[cfg(feature = "vr")]
pub use vr_config::VrConfig;

pub use vr_windows::{Vr<PERSON>indowManager, VrEye};

// Re-export VR API for easy access
pub use vr_api::{
    VrInitResult, SimpleVrScene, SimpleVrObject,
    init_vr_simple, init_vr_with_config, is_vr_available,
    connect_to_headset, render_vr_scene, create_vr_config, create_disabled_vr_config
};

// Provide stub types when VR feature is disabled
#[cfg(not(feature = "vr"))]
pub struct VrRenderer;

#[cfg(not(feature = "vr"))]
pub struct VrCamera;

#[cfg(not(feature = "vr"))]
impl VrCamera {
    pub fn stereo_view_projection(&self) -> [glam::Mat4; 2] {
        [glam::Mat4::IDENTITY, glam::Mat4::IDENTITY]
    }
}

#[cfg(not(feature = "vr"))]
pub struct VrInput;

#[cfg(not(feature = "vr"))]
#[derive(Debug, Clone)]
pub struct VrConfig {
    pub enabled: bool,
    pub application_name: String,
    pub engine_name: String,
}

#[cfg(not(feature = "vr"))]
#[derive(Debug, Clone)]
pub struct VrSceneData {
    pub view_projection: glam::Mat4,
    pub background_color: [f32; 4],
    pub clear_background: bool,
    pub draw_commands: Vec<VrDrawCommand>,
}

#[cfg(not(feature = "vr"))]
#[derive(Debug, Clone)]
pub struct VrDrawCommand {
    pub mesh_id: u64,
    pub transform: glam::Mat4,
}

#[cfg(not(feature = "vr"))]
impl VrRenderer {
    pub fn new(_config: VrConfig, _graphics_backend: crate::graphics::GraphicsBackend) -> anyhow::Result<Self> {
        Ok(Self)
    }

    pub fn is_enabled(&self) -> bool { false }
    pub fn is_session_running(&self) -> bool { false }
    pub fn has_render_targets(&self) -> bool { false }
    pub fn camera(&self) -> Option<&VrCamera> { None }
    pub fn input(&self) -> Option<&VrInput> { None }
    pub fn update(&mut self) -> anyhow::Result<()> { Ok(()) }
    pub fn begin_frame(&mut self) -> anyhow::Result<bool> { Ok(false) }
    pub fn end_frame(&mut self) -> anyhow::Result<()> { Ok(()) }
    pub fn set_scene_data(&mut self, _scene_data: VrSceneData) {}
    pub fn set_scene_data_with_meshes(&mut self, _scene_data: VrSceneData, _meshes: &std::collections::HashMap<u64, crate::graphics::Mesh>) {}
    pub fn set_desktop_camera_data(&mut self, _view_proj: glam::Mat4) {}
    pub fn get_render_targets(&self) -> Vec<&wgpu::TextureView> { Vec::new() }
    pub fn initialize_wgpu_texture_views(&mut self, _device: &wgpu::Device) -> anyhow::Result<()> { Ok(()) }
    pub fn create_test_vr_textures(&mut self, _device: &wgpu::Device) -> anyhow::Result<()> { Ok(()) }
}

#[cfg(not(feature = "vr"))]
impl Default for VrConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            application_name: "Game Engine VR Application".to_string(),
            engine_name: "Game Engine".to_string(),
        }
    }
}

#[cfg(not(feature = "vr"))]
impl Default for VrSceneData {
    fn default() -> Self {
        Self {
            view_projection: glam::Mat4::IDENTITY,
            background_color: [0.0, 0.0, 0.0, 1.0],
            clear_background: true,
            draw_commands: Vec::new(),
        }
    }
}
