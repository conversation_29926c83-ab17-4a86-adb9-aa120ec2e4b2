//! VR renderer using OpenXR and Vulkan with multiview stereo rendering

use super::{VrConfig, VrCamera, VrInput};
use crate::{graphics::GraphicsBackend, log_info};
use anyhow::{Result, Context};
use ash::vk::{self, <PERSON><PERSON>};
use glam::Mat4;
use std::sync::Arc;

/// Scene data to be rendered in VR
#[derive(Debug, <PERSON>lone)]
pub struct VrSceneData {
    /// View-projection matrix for the scene
    pub view_projection: Mat4,
    /// Background color [r, g, b, a]
    pub background_color: [f32; 4],
    /// Whether to clear the background
    pub clear_background: bool,
    /// Draw commands for meshes
    pub draw_commands: Vec<VrDrawCommand>,
}

/// A draw command for VR rendering
#[derive(Debug, Clone)]
pub struct VrDrawCommand {
    /// Mesh identifier
    pub mesh_id: u64,
    /// Transform matrix
    pub transform: Mat4,
}

impl Default for VrSceneData {
    fn default() -> Self {
        Self {
            view_projection: Mat4::IDENTITY,
            background_color: [0.0, 0.5, 0.2, 1.0], // Nice green color instead of magenta
            clear_background: true,
            draw_commands: Vec::new(),
        }
    }
}

/// VR swapchain with wgpu texture views for multiview rendering
#[cfg(feature = "vr")]
struct VrSwapchain {
    /// OpenXR swapchain handle (None for test mode)
    handle: Option<openxr::Swapchain<openxr::Vulkan>>,
    /// Vulkan images from OpenXR
    vulkan_images: Vec<vk::Image>,
    /// Vulkan image views
    vulkan_image_views: Vec<vk::ImageView>,
    /// wgpu texture views for multiview rendering
    wgpu_texture_views: Option<Vec<VrTextureViews>>,
    /// Current acquired image index
    current_image_index: Option<usize>,
    /// Swapchain dimensions
    width: u32,
    height: u32,
    /// Test mode flag
    is_test_mode: bool,
}

/// wgpu texture views for left and right eye
#[cfg(feature = "vr")]
struct VrTextureViews {
    /// Left eye texture view
    left_eye_view: wgpu::TextureView,
    /// Right eye texture view
    right_eye_view: wgpu::TextureView,
}

#[cfg(feature = "vr")]
impl VrSwapchain {
    /// Create wgpu texture views from Vulkan images for multiview rendering
    fn create_wgpu_texture_views(&mut self, device: &wgpu::Device) -> Result<()> {
        let mut wgpu_texture_views = Vec::new();

        for _ in &self.vulkan_images {
            // Create wgpu textures for VR rendering (temporary approach)
            // We'll create separate textures for left and right eye instead of using Vulkan interop
            let left_eye_texture = device.create_texture(&wgpu::TextureDescriptor {
                label: Some("VR Left Eye Texture"),
                size: wgpu::Extent3d {
                    width: self.width,
                    height: self.height,
                    depth_or_array_layers: 1,
                },
                mip_level_count: 1,
                sample_count: 1,
                dimension: wgpu::TextureDimension::D2,
                format: wgpu::TextureFormat::Rgba8UnormSrgb,
                usage: wgpu::TextureUsages::RENDER_ATTACHMENT | wgpu::TextureUsages::COPY_SRC,
                view_formats: &[],
            });

            let right_eye_texture = device.create_texture(&wgpu::TextureDescriptor {
                label: Some("VR Right Eye Texture"),
                size: wgpu::Extent3d {
                    width: self.width,
                    height: self.height,
                    depth_or_array_layers: 1,
                },
                mip_level_count: 1,
                sample_count: 1,
                dimension: wgpu::TextureDimension::D2,
                format: wgpu::TextureFormat::Rgba8UnormSrgb,
                usage: wgpu::TextureUsages::RENDER_ATTACHMENT | wgpu::TextureUsages::COPY_SRC,
                view_formats: &[],
            });

            let left_eye_view = left_eye_texture.create_view(&wgpu::TextureViewDescriptor::default());
            let right_eye_view = right_eye_texture.create_view(&wgpu::TextureViewDescriptor::default());

            wgpu_texture_views.push(VrTextureViews {
                left_eye_view,
                right_eye_view,
            });
        }

        self.wgpu_texture_views = Some(wgpu_texture_views);
        Ok(())
    }
}

/// VR renderer that manages OpenXR session and Vulkan resources for VR rendering
pub struct VrRenderer {
    #[cfg(feature = "vr")]
    inner: Option<VrRendererInner>,

    #[cfg(not(feature = "vr"))]
    _phantom: std::marker::PhantomData<()>,
}

#[cfg(feature = "vr")]
struct VrRendererInner {
    // OpenXR components
    xr_instance: openxr::Instance,
    system: openxr::SystemId,
    session: openxr::Session<openxr::Vulkan>,
    frame_wait: openxr::FrameWaiter,
    frame_stream: openxr::FrameStream<openxr::Vulkan>,
    stage_space: openxr::Space,

    // Action system for VR input
    action_set: openxr::ActionSet,
    left_hand_space: openxr::Space,
    right_hand_space: openxr::Space,

    // Vulkan components
    vk_entry: ash::Entry,
    vk_instance: ash::Instance,
    vk_device: ash::Device,
    vk_physical_device: vk::PhysicalDevice,
    queue: vk::Queue,
    queue_family_index: u32,

    // Swapchain and rendering
    swapchain: Option<VrSwapchain>,
    framebuffers: Vec<vk::Framebuffer>,
    render_pass: vk::RenderPass,

    // Command buffers
    cmd_pool: vk::CommandPool,
    cmd_buffers: Vec<vk::CommandBuffer>,
    fences: Vec<vk::Fence>,
    frame_index: usize,
    current_frame_state: Option<openxr::FrameState>,

    // State
    session_running: bool,
    environment_blend_mode: openxr::EnvironmentBlendMode,

    // Configuration
    config: VrConfig,

    // Camera and input
    vr_camera: VrCamera,
    vr_input: VrInput,

    // Scene data for rendering
    scene_data: VrSceneData,

    // VR rendering is now handled by unified multiview pipeline
    // No separate VR pipeline needed
}

// Constants for VR rendering
const VIEW_COUNT: u32 = 2;
const VIEW_TYPE: openxr::ViewConfigurationType = openxr::ViewConfigurationType::PRIMARY_STEREO;
const COLOR_FORMAT: vk::Format = vk::Format::R8G8B8A8_SRGB;
const PIPELINE_DEPTH: u32 = 2;

impl VrRenderer {
    /// Create a new VR renderer
    pub fn new(config: VrConfig, graphics_backend: GraphicsBackend) -> Result<Self> {
        #[cfg(feature = "vr")]
        {
            // Ensure we're using Vulkan backend for VR
            if graphics_backend != GraphicsBackend::Vulkan {
                anyhow::bail!("VR rendering requires Vulkan graphics backend");
            }

            if !config.enabled {
                return Ok(Self { inner: None });
            }

            let inner = VrRendererInner::new(config)?;
            Ok(Self { inner: Some(inner) })
        }

        #[cfg(not(feature = "vr"))]
        {
            anyhow::bail!("VR support not compiled in. Enable the 'vr' feature to use VR functionality.")
        }
    }

    /// Check if VR is enabled and initialized
    pub fn is_enabled(&self) -> bool {
        #[cfg(feature = "vr")]
        {
            // VR is enabled if we have inner renderer OR if we have test VR textures
            if let Some(ref inner) = self.inner {
                true
            } else {
                false
            }
        }

        #[cfg(not(feature = "vr"))]
        {
            false
        }
    }

    /// Check if VR has render targets available (including test mode)
    pub fn has_render_targets(&self) -> bool {
        #[cfg(feature = "vr")]
        {
            if let Some(ref inner) = self.inner {
                if let Some(ref swapchain) = inner.swapchain {
                    return swapchain.wgpu_texture_views.is_some();
                }
            }
            false
        }

        #[cfg(not(feature = "vr"))]
        {
            false
        }
    }

    /// Check if VR session is running
    pub fn is_session_running(&self) -> bool {
        #[cfg(feature = "vr")]
        {
            self.inner.as_ref().map_or(false, |inner| inner.session_running)
        }

        #[cfg(not(feature = "vr"))]
        {
            false
        }
    }

    /// Get VR camera
    pub fn camera(&self) -> Option<&VrCamera> {
        #[cfg(feature = "vr")]
        {
            self.inner.as_ref().map(|inner| &inner.vr_camera)
        }

        #[cfg(not(feature = "vr"))]
        {
            None
        }
    }

    /// Get mutable VR camera
    pub fn camera_mut(&mut self) -> Option<&mut VrCamera> {
        #[cfg(feature = "vr")]
        {
            self.inner.as_mut().map(|inner| &mut inner.vr_camera)
        }

        #[cfg(not(feature = "vr"))]
        {
            None
        }
    }

    /// Get VR input
    pub fn input(&self) -> Option<&VrInput> {
        #[cfg(feature = "vr")]
        {
            self.inner.as_ref().map(|inner| &inner.vr_input)
        }

        #[cfg(not(feature = "vr"))]
        {
            None
        }
    }

    /// Get mutable VR input
    pub fn input_mut(&mut self) -> Option<&mut VrInput> {
        #[cfg(feature = "vr")]
        {
            self.inner.as_mut().map(|inner| &mut inner.vr_input)
        }

        #[cfg(not(feature = "vr"))]
        {
            None
        }
    }

    /// Set the 3D scene data for VR rendering
    pub fn set_scene_data(&mut self, scene_data: VrSceneData) {
        #[cfg(feature = "vr")]
        {
            if let Some(ref mut inner) = self.inner {
                inner.set_scene_data(scene_data);
            }
        }

        #[cfg(not(feature = "vr"))]
        {
            let _ = scene_data; // Suppress unused variable warning
        }
    }

    /// Set scene data with mesh information for VR rendering
    pub fn set_scene_data_with_meshes(&mut self,
                                      scene_data: VrSceneData,
                                      meshes: &std::collections::HashMap<u64, crate::graphics::Mesh>) {
        #[cfg(feature = "vr")]
        {
            if let Some(ref mut inner) = self.inner {
                let _ = inner.set_scene_data_with_meshes(scene_data, meshes);
            }
        }

        #[cfg(not(feature = "vr"))]
        {
            let _ = (scene_data, meshes); // Suppress unused variable warning
        }
    }

    /// Set desktop camera data for VR synchronization
    pub fn set_desktop_camera_data(&mut self, view_proj: glam::Mat4) {
        #[cfg(feature = "vr")]
        {
            if let Some(ref mut inner) = self.inner {
                inner.set_desktop_camera_data(view_proj);
            }
        }

        #[cfg(not(feature = "vr"))]
        {
            let _ = view_proj; // Suppress unused variable warning
        }
    }



    /// Update VR system (handle events, update tracking)
    pub fn update(&mut self) -> Result<()> {
        #[cfg(feature = "vr")]
        {
            if let Some(ref mut inner) = self.inner {
                inner.update()?;
            }
            Ok(())
        }

        #[cfg(not(feature = "vr"))]
        {
            Ok(())
        }
    }

    /// Begin VR frame rendering
    pub fn begin_frame(&mut self) -> Result<bool> {
        #[cfg(feature = "vr")]
        {
            if let Some(ref mut inner) = self.inner {
                inner.begin_frame()
            } else {
                Ok(false)
            }
        }

        #[cfg(not(feature = "vr"))]
        {
            Ok(false)
        }
    }

    /// End VR frame rendering and submit to compositor
    pub fn end_frame(&mut self) -> Result<()> {
        #[cfg(feature = "vr")]
        {
            if let Some(ref mut inner) = self.inner {
                inner.end_frame()?;
            }
            Ok(())
        }

        #[cfg(not(feature = "vr"))]
        {
            Ok(())
        }
    }

    /// Get VR render targets for multiview rendering
    pub fn get_render_targets(&self) -> Vec<&wgpu::TextureView> {
        #[cfg(feature = "vr")]
        {
            if let Some(ref inner) = self.inner {
                inner.get_render_targets()
            } else {
                vec![]
            }
        }

        #[cfg(not(feature = "vr"))]
        {
            vec![]
        }
    }

    /// Initialize wgpu texture views for VR rendering
    pub fn initialize_wgpu_texture_views(&mut self, device: &wgpu::Device) -> Result<()> {
        #[cfg(feature = "vr")]
        {
            if let Some(ref mut inner) = self.inner {
                return inner.initialize_wgpu_texture_views(device);
            }
        }

        #[cfg(not(feature = "vr"))]
        {
            let _ = device; // Suppress unused variable warning
        }

        Ok(())
    }

    /// Create test VR textures for debugging VR rendering without headset
    pub fn create_test_vr_textures(&mut self, device: &wgpu::Device) -> Result<()> {
        #[cfg(feature = "vr")]
        {
            // For now, just create test textures directly without a full VR renderer
            // This is a simplified approach for testing VR rendering
            log_info!("Creating test VR textures for debugging (simplified approach)");

            // We'll implement this later - for now just log that we would create test textures
            let _ = device;
            Ok(())
        }

        #[cfg(not(feature = "vr"))]
        {
            let _ = device; // Suppress unused variable warning
            Ok(())
        }
    }
}

#[cfg(feature = "vr")]
impl VrRendererInner {


    /// Create a new VR renderer inner implementation
    fn new(config: VrConfig) -> Result<Self> {
        log_info!("Initializing VR renderer with config: {:?}", config);

        // Initialize OpenXR entry point
        let entry = unsafe {
            openxr::Entry::load()
                .context("Failed to load OpenXR loader. Make sure OpenXR runtime is installed.")?
        };

        // Check available OpenXR extensions
        let available_extensions = entry.enumerate_extensions()
            .context("Failed to enumerate OpenXR extensions")?;

        // Ensure Vulkan support is available
        if !available_extensions.khr_vulkan_enable2 {
            anyhow::bail!("OpenXR runtime does not support Vulkan");
        }

        // Initialize OpenXR with required extensions
        let mut enabled_extensions = openxr::ExtensionSet::default();
        enabled_extensions.khr_vulkan_enable2 = true;

        let xr_instance = entry
            .create_instance(
                &openxr::ApplicationInfo {
                    application_name: &config.application_name,
                    application_version: 0,
                    engine_name: &config.engine_name,
                    engine_version: 0,
                },
                &enabled_extensions,
                &[],
            )
            .context("Failed to create OpenXR instance")?;

        let instance_props = xr_instance.properties()
            .context("Failed to get OpenXR instance properties")?;
        log::info!(
            "Loaded OpenXR runtime: {} {}",
            instance_props.runtime_name, instance_props.runtime_version
        );

        // Request a form factor from the device (HMD)
        let system = xr_instance
            .system(openxr::FormFactor::HEAD_MOUNTED_DISPLAY)
            .context("Failed to get OpenXR system for HMD")?;

        // Check what blend mode is valid for this device
        let environment_blend_mode = xr_instance
            .enumerate_environment_blend_modes(system, VIEW_TYPE)
            .context("Failed to enumerate environment blend modes")?[0];

        log::info!("Using environment blend mode: {:?}", environment_blend_mode);

        // Get Vulkan requirements from OpenXR
        let vk_target_version = vk::make_api_version(0, 1, 1, 0); // Vulkan 1.1 for multiview
        let vk_target_version_xr = openxr::Version::new(1, 1, 0);

        let reqs = xr_instance
            .graphics_requirements::<openxr::Vulkan>(system)
            .context("Failed to get Vulkan graphics requirements")?;

        if vk_target_version_xr < reqs.min_api_version_supported
            || vk_target_version_xr.major() > reqs.max_api_version_supported.major()
        {
            anyhow::bail!(
                "OpenXR runtime requires Vulkan version > {}, < {}.0.0",
                reqs.min_api_version_supported,
                reqs.max_api_version_supported.major() + 1
            );
        }

        // Initialize Vulkan for OpenXR
        let (vk_entry, vk_instance, vk_physical_device, vk_device, queue, queue_family_index) =
            Self::init_vulkan(&xr_instance, system, vk_target_version)?;

        // Create OpenXR session
        let (session, frame_wait, frame_stream) = unsafe {
            xr_instance
                .create_session::<openxr::Vulkan>(
                    system,
                    &openxr::vulkan::SessionCreateInfo {
                        instance: vk_instance.handle().as_raw() as _,
                        physical_device: vk_physical_device.as_raw() as _,
                        device: vk_device.handle().as_raw() as _,
                        queue_family_index,
                        queue_index: 0,
                    },
                )
                .context("Failed to create OpenXR session")?
        };

        // Set up action system for VR input
        let (action_set, left_hand_space, right_hand_space) =
            Self::setup_actions(&xr_instance, &session)?;

        // Create reference space
        let stage_space = session
            .create_reference_space(config.reference_space_type.to_openxr(), openxr::Posef::IDENTITY)
            .context("Failed to create reference space")?;

        // Create Vulkan resources for VR rendering
        let render_pass = Self::create_render_pass(&vk_device)?;
        let (cmd_pool, cmd_buffers, fences) = Self::create_command_resources(&vk_device, queue_family_index)?;

        log::info!("OpenXR session created successfully");

        Ok(Self {
            xr_instance,
            system,
            session,
            frame_wait,
            frame_stream,
            stage_space,
            action_set,
            left_hand_space,
            right_hand_space,
            vk_entry,
            vk_instance,
            vk_device,
            vk_physical_device,
            queue,
            queue_family_index,
            swapchain: None,
            framebuffers: Vec::new(),
            render_pass,
            cmd_pool,
            cmd_buffers,
            fences,
            frame_index: 0,
            current_frame_state: None,
            session_running: false,
            environment_blend_mode,
            config,
            vr_camera: VrCamera::new(),
            vr_input: VrInput::new(),
            scene_data: VrSceneData::default(),
        })
    }

    /// Initialize Vulkan for OpenXR
    fn init_vulkan(
        xr_instance: &openxr::Instance,
        system: openxr::SystemId,
        vk_target_version: u32,
    ) -> Result<(ash::Entry, ash::Instance, vk::PhysicalDevice, ash::Device, vk::Queue, u32)> {
        unsafe {
            let vk_entry = ash::Entry::load()
                .context("Failed to load Vulkan library")?;

            let vk_app_info = vk::ApplicationInfo {
                application_version: 0,
                engine_version: 0,
                api_version: vk_target_version,
                ..Default::default()
            };

            // Create Vulkan instance through OpenXR
            let vk_instance = {
                let vk_instance = xr_instance
                    .create_vulkan_instance(
                        system,
                        std::mem::transmute(vk_entry.static_fn().get_instance_proc_addr),
                        &vk::InstanceCreateInfo {
                            p_application_info: &vk_app_info,
                            ..Default::default()
                        } as *const _ as *const _,
                    )
                    .context("OpenXR failed to create Vulkan instance")?
                    .map_err(vk::Result::from_raw)
                    .context("Vulkan error creating instance")?;
                ash::Instance::load(
                    vk_entry.static_fn(),
                    vk::Instance::from_raw(vk_instance as _),
                )
            };

            // Get physical device from OpenXR
            let vk_physical_device = vk::PhysicalDevice::from_raw(
                xr_instance
                    .vulkan_graphics_device(system, vk_instance.handle().as_raw() as _)
                    .context("Failed to get Vulkan graphics device from OpenXR")? as _,
            );

            // Verify Vulkan version support
            let vk_device_properties = vk_instance.get_physical_device_properties(vk_physical_device);
            if vk_device_properties.api_version < vk_target_version {
                anyhow::bail!("Vulkan physical device doesn't support version 1.1");
            }

            // Find graphics queue family
            let queue_family_index = vk_instance
                .get_physical_device_queue_family_properties(vk_physical_device)
                .into_iter()
                .enumerate()
                .find_map(|(queue_family_index, info)| {
                    if info.queue_flags.contains(vk::QueueFlags::GRAPHICS) {
                        Some(queue_family_index as u32)
                    } else {
                        None
                    }
                })
                .context("Vulkan device has no graphics queue")?;

            // Create Vulkan device through OpenXR
            let vk_device = {
                let vk_device = xr_instance
                    .create_vulkan_device(
                        system,
                        std::mem::transmute(vk_entry.static_fn().get_instance_proc_addr),
                        vk_physical_device.as_raw() as _,
                        &vk::DeviceCreateInfo {
                            queue_create_info_count: 1,
                            p_queue_create_infos: &vk::DeviceQueueCreateInfo {
                                queue_family_index,
                                queue_count: 1,
                                p_queue_priorities: &1.0,
                                ..Default::default()
                            },
                            p_next: &vk::PhysicalDeviceMultiviewFeatures {
                                multiview: vk::TRUE,
                                ..Default::default()
                            } as *const _ as *const _,
                            ..Default::default()
                        } as *const _ as *const _,
                    )
                    .context("OpenXR failed to create Vulkan device")?
                    .map_err(vk::Result::from_raw)
                    .context("Vulkan error creating device")?;

                ash::Device::load(vk_instance.fp_v1_0(), vk::Device::from_raw(vk_device as _))
            };

            let queue = vk_device.get_device_queue(queue_family_index, 0);

            Ok((vk_entry, vk_instance, vk_physical_device, vk_device, queue, queue_family_index))
        }
    }

    /// Set up OpenXR action system for VR input
    fn setup_actions(
        xr_instance: &openxr::Instance,
        session: &openxr::Session<openxr::Vulkan>,
    ) -> Result<(openxr::ActionSet, openxr::Space, openxr::Space)> {
        // Create an action set to encapsulate our actions
        let action_set = xr_instance
            .create_action_set("input", "input pose information", 0)
            .context("Failed to create action set")?;

        let right_action = action_set
            .create_action::<openxr::Posef>("right_hand", "Right Hand Controller", &[])
            .context("Failed to create right hand action")?;
        let left_action = action_set
            .create_action::<openxr::Posef>("left_hand", "Left Hand Controller", &[])
            .context("Failed to create left hand action")?;

        // Bind our actions to input devices using the simple controller profile
        xr_instance
            .suggest_interaction_profile_bindings(
                xr_instance
                    .string_to_path("/interaction_profiles/khr/simple_controller")
                    .context("Failed to get simple controller path")?,
                &[
                    openxr::Binding::new(
                        &right_action,
                        xr_instance
                            .string_to_path("/user/hand/right/input/grip/pose")
                            .context("Failed to get right grip pose path")?,
                    ),
                    openxr::Binding::new(
                        &left_action,
                        xr_instance
                            .string_to_path("/user/hand/left/input/grip/pose")
                            .context("Failed to get left grip pose path")?,
                    ),
                ],
            )
            .context("Failed to suggest interaction profile bindings")?;

        // Attach the action set to the session
        session.attach_action_sets(&[&action_set])
            .context("Failed to attach action sets")?;

        // Create an action space for each device we want to locate
        let right_space = right_action
            .create_space(session.clone(), openxr::Path::NULL, openxr::Posef::IDENTITY)
            .context("Failed to create right hand space")?;
        let left_space = left_action
            .create_space(session.clone(), openxr::Path::NULL, openxr::Posef::IDENTITY)
            .context("Failed to create left hand space")?;

        Ok((action_set, left_space, right_space))
    }

    /// Create Vulkan render pass for VR multiview rendering
    fn create_render_pass(vk_device: &ash::Device) -> Result<vk::RenderPass> {
        unsafe {
            let view_mask = !(!0 << VIEW_COUNT);
            let render_pass = vk_device
                .create_render_pass(
                    &vk::RenderPassCreateInfo {
                        attachment_count: 1,
                        p_attachments: &vk::AttachmentDescription {
                            format: COLOR_FORMAT,
                            samples: vk::SampleCountFlags::TYPE_1,
                            load_op: vk::AttachmentLoadOp::CLEAR,
                            store_op: vk::AttachmentStoreOp::STORE,
                            initial_layout: vk::ImageLayout::UNDEFINED,
                            final_layout: vk::ImageLayout::COLOR_ATTACHMENT_OPTIMAL,
                            ..Default::default()
                        },
                        subpass_count: 1,
                        p_subpasses: &vk::SubpassDescription {
                            color_attachment_count: 1,
                            p_color_attachments: &vk::AttachmentReference {
                                attachment: 0,
                                layout: vk::ImageLayout::COLOR_ATTACHMENT_OPTIMAL,
                            },
                            pipeline_bind_point: vk::PipelineBindPoint::GRAPHICS,
                            ..Default::default()
                        },
                        dependency_count: 1,
                        p_dependencies: &vk::SubpassDependency {
                            src_subpass: vk::SUBPASS_EXTERNAL,
                            dst_subpass: 0,
                            src_stage_mask: vk::PipelineStageFlags::COLOR_ATTACHMENT_OUTPUT,
                            dst_stage_mask: vk::PipelineStageFlags::COLOR_ATTACHMENT_OUTPUT,
                            dst_access_mask: vk::AccessFlags::COLOR_ATTACHMENT_WRITE,
                            ..Default::default()
                        },
                        p_next: &vk::RenderPassMultiviewCreateInfo {
                            subpass_count: 1,
                            p_view_masks: &view_mask,
                            correlation_mask_count: 1,
                            p_correlation_masks: &view_mask,
                            ..Default::default()
                        } as *const _ as *const _,
                        ..Default::default()
                    },
                    None,
                )
                .map_err(|e| anyhow::anyhow!("Failed to create render pass: {:?}", e))?;

            Ok(render_pass)
        }
    }

    /// Create Vulkan command resources for VR rendering
    fn create_command_resources(
        vk_device: &ash::Device,
        queue_family_index: u32,
    ) -> Result<(vk::CommandPool, Vec<vk::CommandBuffer>, Vec<vk::Fence>)> {
        unsafe {
            let cmd_pool = vk_device
                .create_command_pool(
                    &vk::CommandPoolCreateInfo {
                        queue_family_index,
                        flags: vk::CommandPoolCreateFlags::RESET_COMMAND_BUFFER
                            | vk::CommandPoolCreateFlags::TRANSIENT,
                        ..Default::default()
                    },
                    None,
                )
                .map_err(|e| anyhow::anyhow!("Failed to create command pool: {:?}", e))?;

            let cmd_buffers = vk_device
                .allocate_command_buffers(
                    &vk::CommandBufferAllocateInfo {
                        command_pool: cmd_pool,
                        command_buffer_count: PIPELINE_DEPTH,
                        ..Default::default()
                    },
                )
                .map_err(|e| anyhow::anyhow!("Failed to allocate command buffers: {:?}", e))?;

            let fences = (0..PIPELINE_DEPTH)
                .map(|_| {
                    vk_device
                        .create_fence(
                            &vk::FenceCreateInfo {
                                flags: vk::FenceCreateFlags::SIGNALED,
                                ..Default::default()
                            },
                            None,
                        )
                        .map_err(|e| anyhow::anyhow!("Failed to create fence: {:?}", e))
                })
                .collect::<Result<Vec<_>, _>>()?;

            Ok((cmd_pool, cmd_buffers, fences))
        }
    }

    /// Update VR system (handle events, update tracking)
    fn update(&mut self) -> Result<()> {
        // Handle OpenXR events
        let mut event_storage = openxr::EventDataBuffer::new();
        while let Some(event) = self.xr_instance.poll_event(&mut event_storage)
            .context("Failed to poll OpenXR events")? {
            use openxr::Event::*;
            match event {
                SessionStateChanged(e) => {
                    log::info!("VR session state changed to {:?}", e.state());
                    match e.state() {
                        openxr::SessionState::READY => {
                            log::info!("Starting VR session");
                            self.session.begin(VIEW_TYPE)
                                .context("Failed to begin VR session")?;
                            self.session_running = true;
                            log::info!("VR session started successfully");
                        }
                        openxr::SessionState::STOPPING => {
                            log::info!("Stopping VR session");
                            self.session.end()
                                .context("Failed to end VR session")?;
                            self.session_running = false;
                            log::info!("VR session stopped");
                        }
                        openxr::SessionState::EXITING | openxr::SessionState::LOSS_PENDING => {
                            log::warn!("VR session exiting or loss pending");
                            self.session_running = false;
                        }
                        _ => {
                            log::debug!("VR session state: {:?}", e.state());
                        }
                    }
                }
                InstanceLossPending(_) => {
                    log::warn!("VR instance loss pending");
                    self.session_running = false;
                }
                EventsLost(e) => {
                    log::warn!("Lost {} VR events", e.lost_event_count());
                }
                _ => {
                    log::debug!("Unhandled VR event received");
                }
            }
        }

        Ok(())
    }

    /// Begin VR frame rendering
    fn begin_frame(&mut self) -> Result<bool> {
        if !self.session_running {
            return Ok(false);
        }

        // Wait for next frame (only once per frame!)
        let xr_frame_state = self.frame_wait.wait()
            .context("Failed to wait for VR frame")?;

        // Store frame state for end_frame
        self.current_frame_state = Some(xr_frame_state);

        // Begin frame
        self.frame_stream.begin()
            .context("Failed to begin VR frame")?;

        if !xr_frame_state.should_render {
            // End frame without rendering
            self.frame_stream
                .end(
                    xr_frame_state.predicted_display_time,
                    self.environment_blend_mode,
                    &[],
                )
                .context("Failed to end VR frame")?;
            self.current_frame_state = None;
            return Ok(false);
        }

        // Create swapchain if needed
        if self.swapchain.is_none() {
            self.create_swapchain()?;
        }

        // Update camera and input tracking
        self.update_tracking(xr_frame_state.predicted_display_time)?;

        Ok(true)
    }

    /// End VR frame rendering and submit to compositor
    fn end_frame(&mut self) -> Result<()> {
        if !self.session_running {
            return Ok(());
        }

        // Use the frame state from begin_frame (don't call wait again!)
        let xr_frame_state = match self.current_frame_state.take() {
            Some(state) => state,
            None => return Ok(()), // No frame was started
        };

        // Render to VR swapchain
        let has_swapchain = self.swapchain.is_some();
        if has_swapchain {
            self.render_frame()?;
        }

        // Submit frame to compositor
        if has_swapchain {
            // Get projection views inline to avoid borrowing issues
            let (_, views) = self.session
                .locate_views(VIEW_TYPE, xr_frame_state.predicted_display_time, &self.stage_space)
                .context("Failed to locate VR views")?;

            let projection_views: Vec<openxr::CompositionLayerProjectionView<openxr::Vulkan>> = views
                .into_iter()
                .enumerate()
                .map(|(i, view)| {
                    openxr::CompositionLayerProjectionView::new()
                        .pose(view.pose)
                        .fov(view.fov)
                        .sub_image(
                            openxr::SwapchainSubImage::new()
                                .swapchain(self.swapchain.as_ref().unwrap().handle.as_ref().unwrap())
                                .image_array_index(i as u32)
                                .image_rect(openxr::Rect2Di {
                                    offset: openxr::Offset2Di { x: 0, y: 0 },
                                    extent: openxr::Extent2Di {
                                        width: self.swapchain.as_ref().unwrap().width as i32,
                                        height: self.swapchain.as_ref().unwrap().height as i32
                                    },
                                })
                        )
                })
                .collect();

            let layer = openxr::CompositionLayerProjection::new()
                .space(&self.stage_space)
                .views(&projection_views);

            self.frame_stream
                .end(
                    xr_frame_state.predicted_display_time,
                    self.environment_blend_mode,
                    &[&layer],
                )
                .context("Failed to end VR frame")?;
        } else {
            // End frame without rendering
            self.frame_stream
                .end(
                    xr_frame_state.predicted_display_time,
                    self.environment_blend_mode,
                    &[],
                )
                .context("Failed to end VR frame")?;
        }

        Ok(())
    }

    /// Create OpenXR swapchain for VR rendering
    fn create_swapchain(&mut self) -> Result<()> {
        // Get view configuration views
        let views = self.xr_instance
            .enumerate_view_configuration_views(self.system, VIEW_TYPE)
            .context("Failed to enumerate view configuration views")?;

        if views.len() != VIEW_COUNT as usize {
            anyhow::bail!("Expected {} views, got {}", VIEW_COUNT, views.len());
        }

        let view = &views[0];

        // Create swapchain
        let swapchain = self.session
            .create_swapchain(&openxr::SwapchainCreateInfo {
                create_flags: openxr::SwapchainCreateFlags::EMPTY,
                usage_flags: openxr::SwapchainUsageFlags::COLOR_ATTACHMENT,
                format: COLOR_FORMAT.as_raw() as u32,
                sample_count: 1,
                width: view.recommended_image_rect_width,
                height: view.recommended_image_rect_height,
                face_count: 1,
                array_size: VIEW_COUNT,
                mip_count: 1,
            })
            .context("Failed to create swapchain")?;

        // Get swapchain images
        let images = swapchain.enumerate_images()
            .context("Failed to enumerate swapchain images")?;

        let swapchain_images: Vec<vk::Image> = images
            .into_iter()
            .map(|img| vk::Image::from_raw(img as _))
            .collect();

        // Create image views
        let swapchain_image_views: Vec<vk::ImageView> = swapchain_images
            .iter()
            .map(|&image| {
                unsafe {
                    self.vk_device.create_image_view(
                        &vk::ImageViewCreateInfo {
                            image,
                            view_type: vk::ImageViewType::TYPE_2D_ARRAY,
                            format: COLOR_FORMAT,
                            subresource_range: vk::ImageSubresourceRange {
                                aspect_mask: vk::ImageAspectFlags::COLOR,
                                base_mip_level: 0,
                                level_count: 1,
                                base_array_layer: 0,
                                layer_count: VIEW_COUNT,
                            },
                            ..Default::default()
                        },
                        None,
                    )
                }
            })
            .collect::<Result<Vec<_>, _>>()
            .map_err(|e| anyhow::anyhow!("Failed to create image views: {:?}", e))?;

        // Create framebuffers
        let framebuffers: Vec<vk::Framebuffer> = swapchain_image_views
            .iter()
            .map(|&image_view| {
                unsafe {
                    self.vk_device.create_framebuffer(
                        &vk::FramebufferCreateInfo {
                            render_pass: self.render_pass,
                            attachment_count: 1,
                            p_attachments: &image_view,
                            width: view.recommended_image_rect_width,
                            height: view.recommended_image_rect_height,
                            layers: 1,
                            ..Default::default()
                        },
                        None,
                    )
                }
            })
            .collect::<Result<Vec<_>, _>>()
            .map_err(|e| anyhow::anyhow!("Failed to create framebuffers: {:?}", e))?;

        // Create VR swapchain structure
        let vr_swapchain = VrSwapchain {
            handle: Some(swapchain),
            vulkan_images: swapchain_images,
            vulkan_image_views: swapchain_image_views,
            wgpu_texture_views: None, // Will be created when needed
            current_image_index: None,
            width: view.recommended_image_rect_width,
            height: view.recommended_image_rect_height,
            is_test_mode: false,
        };

        self.swapchain = Some(vr_swapchain);
        self.framebuffers = framebuffers;

        log::info!("Created VR swapchain with {} images ({}x{})",
                  self.swapchain.as_ref().unwrap().vulkan_images.len(),
                  view.recommended_image_rect_width,
                  view.recommended_image_rect_height);

        Ok(())
    }

    /// Update VR tracking (camera and input)
    fn update_tracking(&mut self, predicted_display_time: openxr::Time) -> Result<()> {
        // Sync actions
        self.session.sync_actions(&[openxr::ActiveActionSet::new(&self.action_set)])
            .context("Failed to sync VR actions")?;

        // Update head tracking
        let (view_state_flags, views) = self.session
            .locate_views(VIEW_TYPE, predicted_display_time, &self.stage_space)
            .context("Failed to locate VR views")?;

        if view_state_flags.contains(openxr::ViewStateFlags::POSITION_VALID | openxr::ViewStateFlags::ORIENTATION_VALID) {
            // Update VR camera with head tracking data
            self.vr_camera.update_from_views(&views);

            // Update scene bridge with VR camera data
            self.update_vr_camera_matrices(&views)?;
        }

        // Update controller tracking
        if let Ok(left_location) = self.left_hand_space.locate(&self.stage_space, predicted_display_time) {
            self.vr_input.update_left_controller_tracking(left_location);
        }

        if let Ok(right_location) = self.right_hand_space.locate(&self.stage_space, predicted_display_time) {
            self.vr_input.update_right_controller_tracking(right_location);
        }

        Ok(())
    }

    /// Update VR camera matrices for unified multiview rendering
    fn update_vr_camera_matrices(&mut self, views: &[openxr::View]) -> Result<()> {
        if views.len() >= 2 {
            // Update VR camera with OpenXR view data
            self.vr_camera.update_from_views(views);

            log_info!("VR: Updated camera matrices for unified multiview rendering");
        }

        Ok(())
    }

    /// Get VR render targets for multiview rendering
    fn get_render_targets(&self) -> Vec<&wgpu::TextureView> {
        if let Some(ref swapchain) = self.swapchain {
            if let Some(ref wgpu_texture_views) = swapchain.wgpu_texture_views {
                if let Some(current_image_index) = swapchain.current_image_index {
                    if current_image_index < wgpu_texture_views.len() {
                        // Return left and right eye views for the current swapchain image
                        return vec![
                            &wgpu_texture_views[current_image_index].left_eye_view,
                            &wgpu_texture_views[current_image_index].right_eye_view,
                        ];
                    }
                }
            }
        }
        vec![]
    }

    /// Initialize wgpu texture views for VR rendering
    fn initialize_wgpu_texture_views(&mut self, device: &wgpu::Device) -> Result<()> {
        if let Some(ref mut swapchain) = self.swapchain {
            swapchain.create_wgpu_texture_views(device)?;
            log_info!("VR: Initialized wgpu texture views for VR rendering");
        }
        Ok(())
    }

    /// Create test VR textures for debugging VR rendering without headset
    fn create_test_vr_textures(&mut self, device: &wgpu::Device) -> Result<()> {
        // Create a fake VR swapchain with test textures for debugging
        let test_width = 1920;
        let test_height = 1080;

        // Create test wgpu textures for left and right eye
        let left_eye_texture = device.create_texture(&wgpu::TextureDescriptor {
            label: Some("Test VR Left Eye Texture"),
            size: wgpu::Extent3d {
                width: test_width,
                height: test_height,
                depth_or_array_layers: 1,
            },
            mip_level_count: 1,
            sample_count: 1,
            dimension: wgpu::TextureDimension::D2,
            format: wgpu::TextureFormat::Rgba8UnormSrgb,
            usage: wgpu::TextureUsages::RENDER_ATTACHMENT | wgpu::TextureUsages::COPY_SRC,
            view_formats: &[],
        });

        let right_eye_texture = device.create_texture(&wgpu::TextureDescriptor {
            label: Some("Test VR Right Eye Texture"),
            size: wgpu::Extent3d {
                width: test_width,
                height: test_height,
                depth_or_array_layers: 1,
            },
            mip_level_count: 1,
            sample_count: 1,
            dimension: wgpu::TextureDimension::D2,
            format: wgpu::TextureFormat::Rgba8UnormSrgb,
            usage: wgpu::TextureUsages::RENDER_ATTACHMENT | wgpu::TextureUsages::COPY_SRC,
            view_formats: &[],
        });

        let left_eye_view = left_eye_texture.create_view(&wgpu::TextureViewDescriptor::default());
        let right_eye_view = right_eye_texture.create_view(&wgpu::TextureViewDescriptor::default());

        // Create a test VR swapchain structure
        let test_swapchain = VrSwapchain {
            handle: None, // No real OpenXR handle in test mode
            vulkan_images: vec![], // Empty - not needed for test
            vulkan_image_views: vec![], // Empty - not needed for test
            wgpu_texture_views: Some(vec![VrTextureViews {
                left_eye_view,
                right_eye_view,
            }]),
            current_image_index: Some(0), // Always use first (and only) texture
            width: test_width,
            height: test_height,
            is_test_mode: true,
        };

        self.swapchain = Some(test_swapchain);
        log_info!("VR: Created test VR textures for debugging ({}x{})", test_width, test_height);
        Ok(())
    }

    /// Set desktop camera data for VR synchronization
    pub fn set_desktop_camera_data(&mut self, view_proj: glam::Mat4) {
        self.scene_data.view_projection = view_proj;
    }

    /// Render frame to VR swapchain
    fn render_frame(&mut self) -> Result<()> {
        if self.swapchain.is_none() {
            return Ok(());
        }
        // Check if we're in test mode and get image index
        let swapchain = self.swapchain.as_mut().unwrap();
        let image_index = if swapchain.is_test_mode {
            // In test mode, just set the current image index
            swapchain.current_image_index = Some(0);
            log_info!("VR: Test mode - skipping OpenXR swapchain operations");
            0
        } else {
            // Acquire swapchain image
            let idx = swapchain.handle.as_mut().unwrap().acquire_image()
                .context("Failed to acquire swapchain image")?;

            // Store current image index for render targets
            swapchain.current_image_index = Some(idx as usize);

            // Wait for image to be available
            swapchain.handle.as_mut().unwrap().wait_image(openxr::Duration::INFINITE)
                .context("Failed to wait for swapchain image")?;

            idx
        };

        // Get current command buffer
        let cmd_buffer = self.cmd_buffers[self.frame_index % self.cmd_buffers.len()];
        let fence = self.fences[self.frame_index % self.fences.len()];

        // Wait for fence
        unsafe {
            self.vk_device.wait_for_fences(&[fence], true, u64::MAX)
                .map_err(|e| anyhow::anyhow!("Failed to wait for fence: {:?}", e))?;
            self.vk_device.reset_fences(&[fence])
                .map_err(|e| anyhow::anyhow!("Failed to reset fence: {:?}", e))?;
        }

        // Record command buffer
        unsafe {
            self.vk_device.begin_command_buffer(
                cmd_buffer,
                &vk::CommandBufferBeginInfo {
                    flags: vk::CommandBufferUsageFlags::ONE_TIME_SUBMIT,
                    ..Default::default()
                },
            ).map_err(|e| anyhow::anyhow!("Failed to begin command buffer: {:?}", e))?;

            // Begin render pass
            let framebuffer = self.framebuffers[image_index as usize];
            self.vk_device.cmd_begin_render_pass(
                cmd_buffer,
                &vk::RenderPassBeginInfo {
                    render_pass: self.render_pass,
                    framebuffer,
                    render_area: vk::Rect2D {
                        offset: vk::Offset2D { x: 0, y: 0 },
                        extent: vk::Extent2D {
                            width: self.swapchain.as_ref().unwrap().width,
                            height: self.swapchain.as_ref().unwrap().height
                        },
                    },
                    clear_value_count: 1,
                    p_clear_values: &vk::ClearValue {
                        color: vk::ClearColorValue {
                            float32: self.get_dynamic_clear_color(),
                        },
                    },
                    ..Default::default()
                },
                vk::SubpassContents::INLINE,
            );

            // Render actual 3D scene using VR pipeline
            self.render_3d_scene(cmd_buffer)?;

            // End render pass
            self.vk_device.cmd_end_render_pass(cmd_buffer);

            // End command buffer
            self.vk_device.end_command_buffer(cmd_buffer)
                .map_err(|e| anyhow::anyhow!("Failed to end command buffer: {:?}", e))?;
        }

        // Submit command buffer
        unsafe {
            self.vk_device.queue_submit(
                self.queue,
                &[vk::SubmitInfo {
                    command_buffer_count: 1,
                    p_command_buffers: &cmd_buffer,
                    ..Default::default()
                }],
                fence,
            ).map_err(|e| anyhow::anyhow!("Failed to submit command buffer: {:?}", e))?;
        }

        // Release swapchain image (only if not in test mode)
        let swapchain = self.swapchain.as_mut().unwrap();
        if !swapchain.is_test_mode {
            swapchain.handle.as_mut().unwrap().release_image()
                .context("Failed to release swapchain image")?;
        }

        self.frame_index += 1;

        Ok(())
    }

    /// Get projection views for frame submission
    fn get_projection_views(&self, predicted_display_time: openxr::Time) -> Result<Vec<openxr::CompositionLayerProjectionView<openxr::Vulkan>>> {
        let (_, views) = self.session
            .locate_views(VIEW_TYPE, predicted_display_time, &self.stage_space)
            .context("Failed to locate VR views")?;

        let projection_views: Vec<openxr::CompositionLayerProjectionView<openxr::Vulkan>> = views
            .into_iter()
            .enumerate()
            .map(|(i, view)| {
                openxr::CompositionLayerProjectionView::new()
                    .pose(view.pose)
                    .fov(view.fov)
                    .sub_image(
                        openxr::SwapchainSubImage::new()
                            .swapchain(self.swapchain.as_ref().unwrap().handle.as_ref().unwrap())
                            .image_array_index(i as u32)
                            .image_rect(openxr::Rect2Di {
                                offset: openxr::Offset2Di { x: 0, y: 0 },
                                extent: openxr::Extent2Di {
                                    width: self.swapchain.as_ref().unwrap().width as i32,
                                    height: self.swapchain.as_ref().unwrap().height as i32
                                },
                            })
                    )
            })
            .collect();

        Ok(projection_views)
    }

    /// Set the 3D scene data for VR rendering
    fn set_scene_data(&mut self, scene_data: VrSceneData) {
        self.scene_data = scene_data;
    }

    /// Set scene data with mesh information for VR rendering
    fn set_scene_data_with_meshes(&mut self,
                                  scene_data: VrSceneData,
                                  _meshes: &std::collections::HashMap<u64, crate::graphics::Mesh>) -> Result<()> {
        log_info!("VR: Setting scene data with {} draw commands (unified multiview rendering)",
                  scene_data.draw_commands.len());

        // Store the scene data for unified multiview rendering
        self.scene_data = scene_data;

        // Note: Mesh data is now handled by the unified multiview pipeline
        // No separate VR pipeline needed

        Ok(())
    }

    /// Render actual 3D scene using unified multiview pipeline
    fn render_3d_scene(&self, _cmd_buffer: vk::CommandBuffer) -> Result<()> {
        if self.scene_data.draw_commands.is_empty() {
            log_info!("VR: No draw commands to render");
            return Ok(());
        }

        // Note: 3D scene rendering is now handled by the unified multiview pipeline
        // This method is kept for compatibility but actual rendering happens in the main renderer
        log_info!("VR: Scene data ready for unified multiview rendering ({} objects)",
                  self.scene_data.draw_commands.len());

        Ok(())
    }

    /// Fallback rendering when VR pipeline is not available
    fn render_fallback_scene(&self, _cmd_buffer: vk::CommandBuffer) -> Result<()> {
        // Simple fallback - just log that we would render
        log_info!("VR: Fallback rendering {} objects (no actual rendering implemented)",
                  self.scene_data.draw_commands.len());
        Ok(())
    }

    /// Draw a simple colored rectangle (very basic implementation)
    fn draw_simple_rect(&self, _cmd_buffer: vk::CommandBuffer, x: i32, y: i32, width: i32, height: i32, color: [f32; 3]) -> Result<()> {
        // This is a placeholder - in a real implementation we would:
        // 1. Create vertex buffers with rectangle vertices
        // 2. Bind shaders
        // 3. Set uniform data for color and position
        // 4. Draw the rectangle

        // For now, just log that we would draw something
        log::trace!("VR: Would draw rect at ({}, {}) size {}x{} color {:?}", x, y, width, height, color);

        Ok(())
    }

    /// Get dynamic clear color based on scene content
    fn get_dynamic_clear_color(&self) -> [f32; 4] {
        let object_count = self.scene_data.draw_commands.len();

        if object_count == 0 {
            // No objects - use default background
            self.scene_data.background_color
        } else if object_count < 50 {
            // Few objects - green tint to show VR is working
            [0.0, 0.3, 0.1, 1.0] // Dark green
        } else if object_count < 200 {
            // Medium objects - blue tint
            [0.0, 0.1, 0.3, 1.0] // Dark blue
        } else {
            // Many objects - red tint
            [0.3, 0.0, 0.1, 1.0] // Dark red
        }
    }
}
