//! High-level VR API for easy integration into games
//! 
//! This module provides simple functions that games can use to:
//! - Initialize VR support
//! - Connect to VR headsets
//! - Render VR scenes
//! - Handle VR input

use super::{VrConfig, VrSceneData, VrDrawCommand};
use crate::graphics::GraphicsBackend;
use glam::Mat4;

/// Simple VR initialization result
#[derive(Debug)]
pub struct VrInitResult {
    /// Whether VR was successfully initialized
    pub success: bool,
    /// Human-readable status message
    pub message: String,
    /// Whether a headset was detected
    pub headset_connected: bool,
}

/// Simple VR rendering data that games can easily construct
#[derive(Debug, Clone)]
pub struct SimpleVrScene {
    /// Background color [r, g, b, a]
    pub background_color: [f32; 4],
    /// Whether to clear the background
    pub clear_background: bool,
    /// List of objects to render
    pub objects: Vec<SimpleVrObject>,
}

/// Simple VR object representation
#[derive(Debug, Clone)]
pub struct SimpleVrObject {
    /// Mesh ID to render
    pub mesh_id: u64,
    /// World transform matrix
    pub transform: Mat4,
}

impl Default for SimpleVrScene {
    fn default() -> Self {
        Self {
            background_color: [0.1, 0.2, 0.3, 1.0],
            clear_background: true,
            objects: Vec::new(),
        }
    }
}

/// Initialize VR support with default settings
/// 
/// This is the simplest way to enable VR in your game.
/// Call this once during engine initialization.
pub fn init_vr_simple(graphics_backend: GraphicsBackend) -> VrInitResult {
    init_vr_with_config(VrConfig::default(), graphics_backend)
}

/// Initialize VR support with custom configuration
/// 
/// Use this if you need to customize VR settings like application name,
/// reference space type, or other advanced options.
pub fn init_vr_with_config(config: VrConfig, graphics_backend: GraphicsBackend) -> VrInitResult {
    #[cfg(feature = "vr")]
    {
        use crate::vr::VrRenderer;

        match VrRenderer::new(config, graphics_backend) {
            Ok(vr_renderer) => {
                let headset_connected = vr_renderer.is_enabled() && vr_renderer.is_session_running();
                VrInitResult {
                    success: true,
                    message: if headset_connected {
                        "VR initialized successfully with headset connected".to_string()
                    } else {
                        "VR initialized but no headset detected".to_string()
                    },
                    headset_connected,
                }
            }
            Err(e) => VrInitResult {
                success: false,
                message: format!("VR initialization failed: {}", e),
                headset_connected: false,
            }
        }
    }
    
    #[cfg(not(feature = "vr"))]
    {
        let _ = (config, graphics_backend); // Suppress unused variable warnings
        VrInitResult {
            success: false,
            message: "VR support not compiled in. Enable the 'vr' feature to use VR functionality.".to_string(),
            headset_connected: false,
        }
    }
}

/// Check if VR is currently available and working
/// 
/// Call this to check VR status at runtime.
pub fn is_vr_available() -> bool {
    #[cfg(feature = "vr")]
    {
        // This would check if VR is actually working
        // For now, just return true if VR feature is enabled
        true
    }
    
    #[cfg(not(feature = "vr"))]
    {
        false
    }
}

/// Connect to VR headset
/// 
/// Call this to establish connection with the VR headset.
/// Returns true if connection was successful.
pub fn connect_to_headset() -> bool {
    #[cfg(feature = "vr")]
    {
        // This would implement actual headset connection logic
        // For now, return true as a placeholder
        true
    }
    
    #[cfg(not(feature = "vr"))]
    {
        false
    }
}

/// Render a simple VR scene
/// 
/// This is the main function games should call to render VR content.
/// Pass in your scene data and this will handle the VR rendering.
pub fn render_vr_scene(scene: SimpleVrScene, camera_view_proj: Mat4) -> VrSceneData {
    // Convert simple scene to internal VR scene data
    let draw_commands: Vec<VrDrawCommand> = scene.objects
        .into_iter()
        .map(|obj| VrDrawCommand {
            mesh_id: obj.mesh_id,
            transform: obj.transform,
        })
        .collect();
    
    VrSceneData {
        view_projection: camera_view_proj,
        background_color: scene.background_color,
        clear_background: scene.clear_background,
        draw_commands,
    }
}

/// Create a simple VR configuration with application name
/// 
/// This is a convenience function to create VR config with just an app name.
pub fn create_vr_config(app_name: &str) -> VrConfig {
    VrConfig {
        enabled: true,
        application_name: app_name.to_string(),
        engine_name: "Game Engine".to_string(),
        ..Default::default()
    }
}

/// Create a disabled VR configuration
/// 
/// Use this when you want to explicitly disable VR.
pub fn create_disabled_vr_config() -> VrConfig {
    VrConfig {
        enabled: false,
        ..Default::default()
    }
}
