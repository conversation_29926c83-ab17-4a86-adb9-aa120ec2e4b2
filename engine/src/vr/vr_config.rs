//! VR configuration and settings

/// VR rendering configuration
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct VrConfig {
    /// Whether VR rendering is enabled
    pub enabled: bool,
    
    /// Application name to report to OpenXR runtime
    pub application_name: String,
    
    /// Engine name to report to OpenXR runtime  
    pub engine_name: String,
    
    /// Whether to use multiview rendering (recommended for performance)
    pub use_multiview: bool,
    
    /// Reference space type for VR tracking
    pub reference_space_type: VrReferenceSpaceType,
    
    /// Whether to enable controller tracking
    pub enable_controller_tracking: bool,
    
    /// Whether to enable haptic feedback
    pub enable_haptics: bool,
    
    /// Maximum number of frames in flight for VR rendering
    pub max_frames_in_flight: u32,
}

impl Default for VrConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            application_name: "Game Engine VR Application".to_string(),
            engine_name: "Game Engine".to_string(),
            use_multiview: true,
            reference_space_type: VrReferenceSpaceType::Stage,
            enable_controller_tracking: true,
            enable_haptics: true,
            max_frames_in_flight: 2,
        }
    }
}

/// VR reference space types for tracking
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>Eq, Eq)]
pub enum VrReferenceSpaceType {
    /// Local space - relative to device starting position
    Local,
    /// Stage space - relative to center of guardian/play area bounds
    Stage,
    /// View space - relative to current head position
    View,
}

impl VrReferenceSpaceType {
    #[cfg(feature = "vr")]
    pub fn to_openxr(self) -> openxr::ReferenceSpaceType {
        match self {
            VrReferenceSpaceType::Local => openxr::ReferenceSpaceType::LOCAL,
            VrReferenceSpaceType::Stage => openxr::ReferenceSpaceType::STAGE,
            VrReferenceSpaceType::View => openxr::ReferenceSpaceType::VIEW,
        }
    }
}

/// VR rendering mode
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum VrRenderMode {
    /// Render only to VR headset
    VrOnly,
    /// Render to both VR headset and desktop window (mirror mode)
    VrWithMirror,
    /// Render only to desktop window (VR disabled)
    DesktopOnly,
}

impl Default for VrRenderMode {
    fn default() -> Self {
        VrRenderMode::DesktopOnly
    }
}
