//! VR camera system with stereo rendering support

use glam::{Mat4, Vec3, Quat};

/// VR camera system that handles stereo rendering for VR headsets
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct VrCamera {
    /// Head pose (position and orientation)
    head_pose: VrPose,
    
    /// Left eye projection matrix
    left_eye_projection: Mat4,
    
    /// Right eye projection matrix  
    right_eye_projection: Mat4,
    
    /// Left eye view matrix (relative to head)
    left_eye_view: Mat4,
    
    /// Right eye view matrix (relative to head)
    right_eye_view: Mat4,
    
    /// Near clipping plane
    near_plane: f32,
    
    /// Far clipping plane
    far_plane: f32,
    
    /// Whether the camera matrices are dirty and need updating
    dirty: bool,
}

/// VR pose representing position and orientation
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>)]
pub struct VrPose {
    pub position: Vec3,
    pub orientation: Quat,
}

impl Default for VrPose {
    fn default() -> Self {
        Self {
            position: Vec3::ZERO,
            orientation: Quat::IDENTITY,
        }
    }
}

impl VrPose {
    /// Create a new VR pose
    pub fn new(position: Vec3, orientation: Quat) -> Self {
        Self { position, orientation }
    }
    
    /// Convert to a transformation matrix
    pub fn to_matrix(&self) -> Mat4 {
        Mat4::from_rotation_translation(self.orientation, self.position)
    }
    
    /// Get the inverse transformation matrix (for view matrix)
    pub fn to_inverse_matrix(&self) -> Mat4 {
        self.to_matrix().inverse()
    }
}

impl VrCamera {
    /// Create a new VR camera
    pub fn new() -> Self {
        Self {
            head_pose: VrPose::default(),
            left_eye_projection: Mat4::IDENTITY,
            right_eye_projection: Mat4::IDENTITY,
            left_eye_view: Mat4::IDENTITY,
            right_eye_view: Mat4::IDENTITY,
            near_plane: 0.01,
            far_plane: 1000.0,
            dirty: true,
        }
    }
    
    /// Update head pose from VR tracking
    pub fn update_head_pose(&mut self, pose: VrPose) {
        self.head_pose = pose;
        self.dirty = true;
    }
    
    /// Update projection matrices from VR system
    pub fn update_projection_matrices(&mut self, left_projection: Mat4, right_projection: Mat4) {
        self.left_eye_projection = left_projection;
        self.right_eye_projection = right_projection;
        self.dirty = true;
    }
    
    /// Update view matrices from VR system (eye poses relative to head)
    pub fn update_view_matrices(&mut self, left_eye_pose: VrPose, right_eye_pose: VrPose) {
        // Combine head pose with eye poses to get world-space eye positions
        let head_transform = self.head_pose.to_matrix();
        
        let left_eye_world = head_transform * left_eye_pose.to_matrix();
        let right_eye_world = head_transform * right_eye_pose.to_matrix();
        
        // View matrices are inverse of eye world transforms
        self.left_eye_view = left_eye_world.inverse();
        self.right_eye_view = right_eye_world.inverse();
        
        self.dirty = true;
    }
    
    /// Get the left eye view-projection matrix
    pub fn left_eye_view_projection(&self) -> Mat4 {
        self.left_eye_projection * self.left_eye_view
    }
    
    /// Get the right eye view-projection matrix
    pub fn right_eye_view_projection(&self) -> Mat4 {
        self.right_eye_projection * self.right_eye_view
    }
    
    /// Get both eye view-projection matrices as an array [left, right]
    pub fn stereo_view_projection(&self) -> [Mat4; 2] {
        [
            self.left_eye_view_projection(),
            self.right_eye_view_projection(),
        ]
    }
    
    /// Get the head pose
    pub fn head_pose(&self) -> VrPose {
        self.head_pose
    }
    
    /// Get the head position
    pub fn head_position(&self) -> Vec3 {
        self.head_pose.position
    }
    
    /// Get the head orientation
    pub fn head_orientation(&self) -> Quat {
        self.head_pose.orientation
    }
    
    /// Get the forward direction of the head
    pub fn head_forward(&self) -> Vec3 {
        self.head_pose.orientation * Vec3::NEG_Z
    }
    
    /// Get the right direction of the head
    pub fn head_right(&self) -> Vec3 {
        self.head_pose.orientation * Vec3::X
    }
    
    /// Get the up direction of the head
    pub fn head_up(&self) -> Vec3 {
        self.head_pose.orientation * Vec3::Y
    }
    
    /// Set clipping planes
    pub fn set_clipping_planes(&mut self, near: f32, far: f32) {
        self.near_plane = near;
        self.far_plane = far;
        self.dirty = true;
    }
    
    /// Check if camera matrices need updating
    pub fn is_dirty(&self) -> bool {
        self.dirty
    }
    
    /// Mark camera as clean (matrices updated)
    pub fn mark_clean(&mut self) {
        self.dirty = false;
    }

    /// Update camera from OpenXR view data
    #[cfg(feature = "vr")]
    pub fn update_from_views(&mut self, views: &[openxr::View]) {
        if views.len() >= 2 {
            // Update left eye
            let left_view = &views[0];
            let left_eye_pose = VrPose {
                position: Vec3::new(
                    left_view.pose.position.x,
                    left_view.pose.position.y,
                    left_view.pose.position.z,
                ),
                orientation: Quat::from_xyzw(
                    left_view.pose.orientation.x,
                    left_view.pose.orientation.y,
                    left_view.pose.orientation.z,
                    left_view.pose.orientation.w,
                ),
            };

            // Update right eye
            let right_view = &views[1];
            let right_eye_pose = VrPose {
                position: Vec3::new(
                    right_view.pose.position.x,
                    right_view.pose.position.y,
                    right_view.pose.position.z,
                ),
                orientation: Quat::from_xyzw(
                    right_view.pose.orientation.x,
                    right_view.pose.orientation.y,
                    right_view.pose.orientation.z,
                    right_view.pose.orientation.w,
                ),
            };

            // Calculate head pose as average of eyes
            self.head_pose.position = (left_eye_pose.position + right_eye_pose.position) * 0.5;
            self.head_pose.orientation = left_eye_pose.orientation.slerp(right_eye_pose.orientation, 0.5);

            // Update projection matrices
            self.left_eye_projection = Self::fov_to_projection_matrix(left_view.fov, self.near_plane, self.far_plane);
            self.right_eye_projection = Self::fov_to_projection_matrix(right_view.fov, self.near_plane, self.far_plane);

            // Update view matrices (eyes relative to head)
            let head_inverse = self.head_pose.to_inverse_matrix();
            self.left_eye_view = head_inverse * left_eye_pose.to_inverse_matrix();
            self.right_eye_view = head_inverse * right_eye_pose.to_inverse_matrix();

            self.dirty = true;
        }
    }

    /// Convert OpenXR FOV to projection matrix
    #[cfg(feature = "vr")]
    fn fov_to_projection_matrix(fov: openxr::Fovf, near: f32, far: f32) -> Mat4 {
        let left = (fov.angle_left).tan() * near;
        let right = (fov.angle_right).tan() * near;
        let top = (fov.angle_up).tan() * near;
        let bottom = (fov.angle_down).tan() * near;

        // Create frustum projection matrix manually
        let x = 2.0 * near / (right - left);
        let y = 2.0 * near / (top - bottom);
        let a = (right + left) / (right - left);
        let b = (top + bottom) / (top - bottom);
        let c = -(far + near) / (far - near);
        let d = -2.0 * far * near / (far - near);

        Mat4::from_cols(
            glam::Vec4::new(x, 0.0, 0.0, 0.0),
            glam::Vec4::new(0.0, y, 0.0, 0.0),
            glam::Vec4::new(a, b, c, -1.0),
            glam::Vec4::new(0.0, 0.0, d, 0.0),
        )
    }
}

impl Default for VrCamera {
    fn default() -> Self {
        Self::new()
    }
}
