pub mod core;
pub mod graphics;
pub mod input;
pub mod math;
pub mod resources;
pub mod time;
pub mod profiler;
pub mod debug;
pub mod camera;
pub mod logging;
pub mod assets;
pub mod vr;

// Re-export commonly used types
pub use core::*;
pub use graphics::*;
pub use input::*;
pub use math::*;
pub use resources::*;
pub use time::*;
pub use profiler::*;
pub use debug::*;
pub use camera::*;
pub use logging::*;
pub use assets::*;
pub use vr::*;

// Re-export external dependencies for convenience
pub use glam;
pub use wgpu;
pub use winit;
pub use bytemuck;

/// Initialize the engine with default settings
pub fn init() -> anyhow::Result<()> {
    env_logger::init();
    Ok(())
}

/// Engine version information
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_engine_init() {
        assert!(init().is_ok());
    }
}
