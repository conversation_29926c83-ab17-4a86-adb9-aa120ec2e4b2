use bytemuck::{Pod, Zeroable};
use glam::{Vec2, Vec3};

#[repr(C)]
#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, Pod, Zeroable)]
pub struct Vertex {
    pub position: [f32; 3],
    pub normal: [f32; 3],
    pub tex_coords: [f32; 2],
}

impl Vertex {
    pub fn new(position: Vec3, normal: Vec3, tex_coords: Vec2) -> Self {
        Self {
            position: position.to_array(),
            normal: normal.to_array(),
            tex_coords: tex_coords.to_array(),
        }
    }

    pub fn desc<'a>() -> wgpu::VertexBufferLayout<'a> {
        wgpu::VertexBufferLayout {
            array_stride: std::mem::size_of::<Vertex>() as wgpu::BufferAddress,
            step_mode: wgpu::VertexStepMode::Vertex,
            attributes: &[
                // Position
                wgpu::VertexAttribute {
                    offset: 0,
                    shader_location: 0,
                    format: wgpu::VertexFormat::Float32x3,
                },
                // Normal
                wgpu::VertexAttribute {
                    offset: std::mem::size_of::<[f32; 3]>() as wgpu::BufferAddress,
                    shader_location: 1,
                    format: wgpu::VertexFormat::Float32x3,
                },
                // Texture coordinates
                wgpu::VertexAttribute {
                    offset: std::mem::size_of::<[f32; 6]>() as wgpu::BufferAddress,
                    shader_location: 2,
                    format: wgpu::VertexFormat::Float32x2,
                },
            ],
        }
    }
}

pub struct Mesh {
    pub vertices: Vec<Vertex>,
    pub indices: Vec<u32>,
    pub vertex_buffer: Option<wgpu::Buffer>,
    pub index_buffer: Option<wgpu::Buffer>,
}

impl Mesh {
    pub fn new(vertices: Vec<Vertex>, indices: Vec<u32>) -> Self {
        Self {
            vertices,
            indices,
            vertex_buffer: None,
            index_buffer: None,
        }
    }

    pub fn create_cube() -> Self {
        let vertices = vec![
            // Front face
            Vertex::new(Vec3::new(-1.0, -1.0, 1.0), Vec3::Z, Vec2::new(0.0, 1.0)),
            Vertex::new(Vec3::new(1.0, -1.0, 1.0), Vec3::Z, Vec2::new(1.0, 1.0)),
            Vertex::new(Vec3::new(1.0, 1.0, 1.0), Vec3::Z, Vec2::new(1.0, 0.0)),
            Vertex::new(Vec3::new(-1.0, 1.0, 1.0), Vec3::Z, Vec2::new(0.0, 0.0)),

            // Back face
            Vertex::new(Vec3::new(-1.0, -1.0, -1.0), Vec3::NEG_Z, Vec2::new(1.0, 1.0)),
            Vertex::new(Vec3::new(-1.0, 1.0, -1.0), Vec3::NEG_Z, Vec2::new(1.0, 0.0)),
            Vertex::new(Vec3::new(1.0, 1.0, -1.0), Vec3::NEG_Z, Vec2::new(0.0, 0.0)),
            Vertex::new(Vec3::new(1.0, -1.0, -1.0), Vec3::NEG_Z, Vec2::new(0.0, 1.0)),
        ];

        let indices = vec![
            // Front face
            0, 1, 2, 2, 3, 0,
            // Back face
            4, 5, 6, 6, 7, 4,
            // Left face
            4, 0, 3, 3, 5, 4,
            // Right face
            1, 7, 6, 6, 2, 1,
            // Top face
            3, 2, 6, 6, 5, 3,
            // Bottom face
            4, 7, 1, 1, 0, 4,
        ];

        Self::new(vertices, indices)
    }

    pub fn create_plane() -> Self {
        let vertices = vec![
            Vertex::new(Vec3::new(-1.0, 0.0, -1.0), Vec3::Y, Vec2::new(0.0, 1.0)),
            Vertex::new(Vec3::new(1.0, 0.0, -1.0), Vec3::Y, Vec2::new(1.0, 1.0)),
            Vertex::new(Vec3::new(1.0, 0.0, 1.0), Vec3::Y, Vec2::new(1.0, 0.0)),
            Vertex::new(Vec3::new(-1.0, 0.0, 1.0), Vec3::Y, Vec2::new(0.0, 0.0)),
        ];

        let indices = vec![0, 1, 2, 2, 3, 0];

        Self::new(vertices, indices)
    }

    pub fn upload_to_gpu(&mut self, device: &wgpu::Device) {
        self.upload_to_gpu_with_profiler(device, None);
    }

    pub fn upload_to_gpu_with_profiler(&mut self, device: &wgpu::Device, profiler: Option<&mut crate::profiler::Profiler>) {
        use wgpu::util::DeviceExt;

        let vertex_data = bytemuck::cast_slice(&self.vertices);
        let index_data = bytemuck::cast_slice(&self.indices);

        self.vertex_buffer = Some(device.create_buffer_init(&wgpu::util::BufferInitDescriptor {
            label: Some("Vertex Buffer"),
            contents: vertex_data,
            usage: wgpu::BufferUsages::VERTEX,
        }));

        self.index_buffer = Some(device.create_buffer_init(&wgpu::util::BufferInitDescriptor {
            label: Some("Index Buffer"),
            contents: index_data,
            usage: wgpu::BufferUsages::INDEX,
        }));

        // Track VRAM usage if profiler is provided
        if let Some(profiler) = profiler {
            profiler.add_buffer_memory(vertex_data.len() as u64);
            profiler.add_buffer_memory(index_data.len() as u64);
        }
    }

    /// Get the vertex buffer (must call upload_to_gpu first)
    pub fn vertex_buffer(&self) -> &wgpu::Buffer {
        self.vertex_buffer.as_ref().expect("Vertex buffer not uploaded. Call upload_to_gpu() first.")
    }

    /// Get the index buffer (must call upload_to_gpu first)
    pub fn index_buffer(&self) -> &wgpu::Buffer {
        self.index_buffer.as_ref().expect("Index buffer not uploaded. Call upload_to_gpu() first.")
    }

    /// Get the number of indices
    pub fn index_count(&self) -> u32 {
        self.indices.len() as u32
    }

    /// Check if buffers are uploaded to GPU
    pub fn is_uploaded(&self) -> bool {
        self.vertex_buffer.is_some() && self.index_buffer.is_some()
    }

    /// Get the total GPU memory usage of this mesh in bytes
    pub fn gpu_memory_usage(&self) -> u64 {
        let vertex_size = self.vertices.len() * std::mem::size_of::<Vertex>();
        let index_size = self.indices.len() * std::mem::size_of::<u32>();
        (vertex_size + index_size) as u64
    }

    /// Create a triangle mesh for testing
    pub fn create_triangle() -> Self {
        let vertices = vec![
            Vertex::new(Vec3::new(0.0, 0.5, 0.0), Vec3::Z, Vec2::new(0.5, 0.0)),
            Vertex::new(Vec3::new(-0.5, -0.5, 0.0), Vec3::Z, Vec2::new(0.0, 1.0)),
            Vertex::new(Vec3::new(0.5, -0.5, 0.0), Vec3::Z, Vec2::new(1.0, 1.0)),
        ];

        let indices = vec![0, 1, 2];

        Self::new(vertices, indices)
    }

    /// Create a sphere mesh with the given number of segments
    pub fn create_sphere(radius: f32, segments: u32, rings: u32) -> Self {
        let mut vertices = Vec::new();
        let mut indices = Vec::new();

        // Generate vertices
        for ring in 0..=rings {
            let phi = std::f32::consts::PI * ring as f32 / rings as f32;
            let y = radius * phi.cos();
            let ring_radius = radius * phi.sin();

            for segment in 0..=segments {
                let theta = 2.0 * std::f32::consts::PI * segment as f32 / segments as f32;
                let x = ring_radius * theta.cos();
                let z = ring_radius * theta.sin();

                let position = Vec3::new(x, y, z);
                let normal = position.normalize();
                let tex_coords = Vec2::new(
                    segment as f32 / segments as f32,
                    ring as f32 / rings as f32,
                );

                vertices.push(Vertex::new(position, normal, tex_coords));
            }
        }

        // Generate indices
        for ring in 0..rings {
            for segment in 0..segments {
                let current = ring * (segments + 1) + segment;
                let next = current + segments + 1;

                // First triangle
                indices.push(current);
                indices.push(next);
                indices.push(current + 1);

                // Second triangle
                indices.push(current + 1);
                indices.push(next);
                indices.push(next + 1);
            }
        }

        Self::new(vertices, indices)
    }

    /// Create a default sphere (radius 1.0, 32 segments, 16 rings)
    pub fn create_sphere_default() -> Self {
        Self::create_sphere(1.0, 32, 16)
    }
}
