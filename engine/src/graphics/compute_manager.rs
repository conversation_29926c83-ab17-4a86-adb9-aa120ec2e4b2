use crate::assets::ShaderManager;
use std::sync::Arc;
use std::collections::HashMap;
use wgpu::{<PERSON><PERSON>, Que<PERSON>, B<PERSON><PERSON>, BindGroup, BindGroupLayout};
use bytemuck::{Pod, Zeroable};

/// Represents a buffer resource for compute operations
pub struct ComputeBuffer {
    pub buffer: Buffer,
    pub size: u64,
    pub usage: wgpu::BufferUsages,
}

/// Represents a complete compute job with all necessary resources
pub struct ComputeJob {
    pub name: String,
    pub shader_name: String,
    pub bind_group_layout: BindGroupLayout,
    pub bind_group: BindGroup,
    pub buffers: Vec<ComputeBuffer>,
    pub workgroup_size: (u32, u32, u32),
}

/// General-purpose compute shader manager
///
/// This system allows you to:
/// 1. Create compute jobs with arbitrary parameters and data
/// 2. Execute compute shaders with custom workgroup sizes
/// 3. Update uniform data at runtime
/// 4. Manage multiple compute jobs simultaneously
///
/// Example usage:
/// ```rust
/// // Create a compute job
/// let storage_data: Vec<f32> = (0..1024).map(|i| i as f32).collect();
/// let uniform_data = bytemuck::cast_slice(&[params]);
///
/// compute_manager.create_job(
///     "my_job",
///     "my_shader",
///     "my_shader.comp.wgsl",
///     &storage_data,
///     uniform_data,
///     (64, 1, 1), // workgroup size
///     shader_manager,
/// )?;
///
/// // Execute the job
/// compute_manager.execute_job("my_job", shader_manager)?;
///
/// // Update parameters
/// compute_manager.update_uniform_data("my_job", new_uniform_data)?;
/// ```
pub struct ComputeManager {
    device: Arc<Device>,
    queue: Arc<Queue>,
    jobs: HashMap<String, ComputeJob>,
}

impl ComputeManager {
    /// Create a new compute manager
    pub fn new(device: Arc<Device>, queue: Arc<Queue>) -> Self {
        Self {
            device,
            queue,
            jobs: HashMap::new(),
        }
    }

    /// Create a new compute job with storage and uniform buffers
    pub fn create_job<T: Pod + Zeroable>(
        &mut self,
        job_name: &str,
        shader_name: &str,
        shader_filename: &str,
        storage_data: &[T],
        uniform_data: &[u8],
        workgroup_size: (u32, u32, u32),
        shader_manager: &mut ShaderManager,
    ) -> Result<(), String> {
        use wgpu::util::DeviceExt;

        // Create bind group layout
        let bind_group_layout = self.device.create_bind_group_layout(&wgpu::BindGroupLayoutDescriptor {
            label: Some(&format!("{}_bind_group_layout", job_name)),
            entries: &[
                // Storage buffer for data
                wgpu::BindGroupLayoutEntry {
                    binding: 0,
                    visibility: wgpu::ShaderStages::COMPUTE,
                    ty: wgpu::BindingType::Buffer {
                        ty: wgpu::BufferBindingType::Storage { read_only: false },
                        has_dynamic_offset: false,
                        min_binding_size: None,
                    },
                    count: None,
                },
                // Uniform buffer for parameters
                wgpu::BindGroupLayoutEntry {
                    binding: 1,
                    visibility: wgpu::ShaderStages::COMPUTE,
                    ty: wgpu::BindingType::Buffer {
                        ty: wgpu::BufferBindingType::Uniform,
                        has_dynamic_offset: false,
                        min_binding_size: None,
                    },
                    count: None,
                },
            ],
        });

        // Register the compute shader
        shader_manager.register_compute_shader(
            shader_name,
            shader_filename,
            &[&bind_group_layout],
        )?;

        // Create storage buffer
        let storage_buffer = self.device.create_buffer_init(&wgpu::util::BufferInitDescriptor {
            label: Some(&format!("{}_storage_buffer", job_name)),
            contents: bytemuck::cast_slice(storage_data),
            usage: wgpu::BufferUsages::STORAGE | wgpu::BufferUsages::COPY_SRC | wgpu::BufferUsages::COPY_DST,
        });

        // Create uniform buffer
        let uniform_buffer = self.device.create_buffer_init(&wgpu::util::BufferInitDescriptor {
            label: Some(&format!("{}_uniform_buffer", job_name)),
            contents: uniform_data,
            usage: wgpu::BufferUsages::UNIFORM | wgpu::BufferUsages::COPY_DST,
        });

        // Create bind group
        let bind_group = self.device.create_bind_group(&wgpu::BindGroupDescriptor {
            label: Some(&format!("{}_bind_group", job_name)),
            layout: &bind_group_layout,
            entries: &[
                wgpu::BindGroupEntry {
                    binding: 0,
                    resource: storage_buffer.as_entire_binding(),
                },
                wgpu::BindGroupEntry {
                    binding: 1,
                    resource: uniform_buffer.as_entire_binding(),
                },
            ],
        });

        // Create compute buffers
        let buffers = vec![
            ComputeBuffer {
                buffer: storage_buffer,
                size: (storage_data.len() * std::mem::size_of::<T>()) as u64,
                usage: wgpu::BufferUsages::STORAGE | wgpu::BufferUsages::COPY_SRC | wgpu::BufferUsages::COPY_DST,
            },
            ComputeBuffer {
                buffer: uniform_buffer,
                size: uniform_data.len() as u64,
                usage: wgpu::BufferUsages::UNIFORM | wgpu::BufferUsages::COPY_DST,
            },
        ];

        // Create the compute job
        let job = ComputeJob {
            name: job_name.to_string(),
            shader_name: shader_name.to_string(),
            bind_group_layout,
            bind_group,
            buffers,
            workgroup_size,
        };

        self.jobs.insert(job_name.to_string(), job);
        Ok(())
    }

    /// Execute a compute job
    pub fn execute_job(&self, job_name: &str, shader_manager: &ShaderManager) -> Result<(), String> {
        let job = self.jobs.get(job_name)
            .ok_or_else(|| format!("Compute job '{}' not found", job_name))?;

        let pipeline = shader_manager.get_compute_pipeline(&job.shader_name)
            .ok_or_else(|| format!("Compute pipeline '{}' not found", job.shader_name))?;

        // Create command encoder
        let mut encoder = self.device.create_command_encoder(&wgpu::CommandEncoderDescriptor {
            label: Some(&format!("{}_encoder", job_name)),
        });

        // Begin compute pass
        {
            let mut compute_pass = encoder.begin_compute_pass(&wgpu::ComputePassDescriptor {
                label: Some(&format!("{}_pass", job_name)),
                timestamp_writes: None,
            });

            compute_pass.set_pipeline(pipeline);
            compute_pass.set_bind_group(0, &job.bind_group, &[]);
            compute_pass.dispatch_workgroups(job.workgroup_size.0, job.workgroup_size.1, job.workgroup_size.2);
        }

        // Submit the command buffer
        self.queue.submit(std::iter::once(encoder.finish()));
        Ok(())
    }

    /// Update uniform buffer data for a compute job
    pub fn update_uniform_data(&self, job_name: &str, data: &[u8]) -> Result<(), String> {
        let job = self.jobs.get(job_name)
            .ok_or_else(|| format!("Compute job '{}' not found", job_name))?;

        // Assume uniform buffer is at index 1 (based on our bind group layout)
        if job.buffers.len() > 1 {
            self.queue.write_buffer(&job.buffers[1].buffer, 0, data);
            Ok(())
        } else {
            Err("No uniform buffer found in compute job".to_string())
        }
    }

    /// Read back storage buffer data from GPU
    ///
    /// This method performs a complete GPU-to-CPU readback operation:
    /// 1. Creates a staging buffer with MAP_READ usage
    /// 2. Copies data from the compute job's storage buffer to staging buffer
    /// 3. Maps the staging buffer and reads the data
    /// 4. Converts the raw bytes back to the specified type T
    ///
    /// Note: This is a synchronous operation that blocks until the GPU operation completes.
    pub fn read_storage_data<T: Pod + Zeroable + Clone>(&self, job_name: &str) -> Result<Vec<T>, String> {
        let job = self.jobs.get(job_name)
            .ok_or_else(|| format!("Compute job '{}' not found", job_name))?;

        // Get the storage buffer (should be at index 0 based on our bind group layout)
        if job.buffers.is_empty() {
            return Err("No buffers found in compute job".to_string());
        }

        let storage_buffer = &job.buffers[0]; // Storage buffer is at index 0
        let buffer_size = storage_buffer.size;

        // Validate that the buffer size is compatible with type T
        let element_size = std::mem::size_of::<T>() as u64;
        if buffer_size % element_size != 0 {
            return Err(format!(
                "Buffer size ({} bytes) is not compatible with element size ({} bytes)",
                buffer_size, element_size
            ));
        }

        let element_count = buffer_size / element_size;

        // Step 1: Create a staging buffer with MAP_READ usage
        let staging_buffer = self.device.create_buffer(&wgpu::BufferDescriptor {
            label: Some(&format!("{}_readback_staging", job_name)),
            size: buffer_size,
            usage: wgpu::BufferUsages::MAP_READ | wgpu::BufferUsages::COPY_DST,
            mapped_at_creation: false,
        });

        // Step 2: Create command encoder and copy from storage buffer to staging buffer
        let mut encoder = self.device.create_command_encoder(&wgpu::CommandEncoderDescriptor {
            label: Some(&format!("{}_readback_encoder", job_name)),
        });

        encoder.copy_buffer_to_buffer(
            &storage_buffer.buffer,  // Source: compute job's storage buffer
            0,                       // Source offset
            &staging_buffer,         // Destination: staging buffer
            0,                       // Destination offset
            buffer_size,             // Size to copy
        );

        // Step 3: Submit the copy command and wait for completion
        self.queue.submit(std::iter::once(encoder.finish()));

        // Step 4: Map the staging buffer for reading
        let buffer_slice = staging_buffer.slice(..);

        // Use pollster to handle the async mapping operation synchronously
        let (sender, receiver) = std::sync::mpsc::channel();
        buffer_slice.map_async(wgpu::MapMode::Read, move |result| {
            sender.send(result).unwrap();
        });

        // Poll the device until the mapping operation completes
        self.device.poll(wgpu::Maintain::Wait);

        // Wait for the mapping to complete
        let map_result = receiver.recv()
            .map_err(|e| format!("Failed to receive mapping result: {}", e))?;

        map_result.map_err(|e| format!("Failed to map staging buffer: {:?}", e))?;

        // Step 5: Read the mapped data and convert it back to type T
        let mapped_data = buffer_slice.get_mapped_range();

        // Ensure the data size matches our expectations
        if mapped_data.len() as u64 != buffer_size {
            return Err(format!(
                "Mapped data size ({} bytes) doesn't match expected size ({} bytes)",
                mapped_data.len(), buffer_size
            ));
        }

        // Convert the raw bytes to Vec<T> using bytemuck
        let result_data: Vec<T> = bytemuck::cast_slice(&mapped_data).to_vec();

        // Verify the element count matches our calculation
        if result_data.len() as u64 != element_count {
            return Err(format!(
                "Element count mismatch: expected {}, got {}",
                element_count, result_data.len()
            ));
        }

        // Step 6: Unmap the buffer (this happens automatically when mapped_data is dropped)
        drop(mapped_data);
        staging_buffer.unmap();

        Ok(result_data)
    }

    /// Get information about a compute job
    pub fn get_job_info(&self, job_name: &str) -> Option<&ComputeJob> {
        self.jobs.get(job_name)
    }

    /// List all available compute jobs
    pub fn list_jobs(&self) -> Vec<&str> {
        self.jobs.keys().map(|s| s.as_str()).collect()
    }

    /// Remove a compute job
    pub fn remove_job(&mut self, job_name: &str) -> bool {
        self.jobs.remove(job_name).is_some()
    }
}
