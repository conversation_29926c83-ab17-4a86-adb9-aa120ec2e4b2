use glam::Vec3;

#[derive(Debu<PERSON>, Clone)]
pub struct Material {
    pub albedo: Vec3,
    pub metallic: f32,
    pub roughness: f32,
    pub emission: Vec3,
    pub texture_id: Option<u32>,
}

impl Material {
    pub fn new() -> Self {
        Self {
            albedo: Vec3::new(1.0, 1.0, 1.0),
            metallic: 0.0,
            roughness: 0.5,
            emission: Vec3::ZERO,
            texture_id: None,
        }
    }

    pub fn with_albedo(mut self, albedo: Vec3) -> Self {
        self.albedo = albedo;
        self
    }

    pub fn with_metallic(mut self, metallic: f32) -> Self {
        self.metallic = metallic;
        self
    }

    pub fn with_roughness(mut self, roughness: f32) -> Self {
        self.roughness = roughness;
        self
    }

    pub fn with_emission(mut self, emission: Vec3) -> Self {
        self.emission = emission;
        self
    }

    pub fn with_texture(mut self, texture_id: u32) -> Self {
        self.texture_id = Some(texture_id);
        self
    }
}

impl Default for Material {
    fn default() -> Self {
        Self::new()
    }
}
