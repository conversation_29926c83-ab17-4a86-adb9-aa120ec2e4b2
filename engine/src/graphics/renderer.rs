use winit::window::Window;
use std::sync::Arc;
use crate::graphics::renderer_3d::Renderer3D;
use crate::graphics::simple_frame_pipeline::SimpleFramePipeline;
use crate::graphics::compute_manager::ComputeManager;

/// Graphics backend configuration options
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq)]
pub enum GraphicsBackend {
    /// Automatic backend selection (default) - chooses the best available backend
    Auto,
    /// Vulkan backend (preferred on Windows/Linux)
    Vulkan,
    /// DirectX 12 backend (Windows only)
    DirectX12,
    /// Metal backend (macOS/iOS only)
    Metal,
    /// OpenGL ES backend (fallback, widely compatible)
    OpenGL,
    /// WebGPU backend (web only)
    WebGPU,
}

impl Default for GraphicsBackend {
    fn default() -> Self {
        GraphicsBackend::Auto
    }
}

impl GraphicsBackend {
    /// Convert GraphicsBackend to wgpu Backends flags
    pub fn to_wgpu_backends(self) -> wgpu::Backends {
        match self {
            GraphicsBackend::Auto => wgpu::Backends::all(),
            GraphicsBackend::Vulkan => wgpu::Backends::VULKAN,
            GraphicsBackend::DirectX12 => wgpu::Backends::DX12,
            GraphicsBackend::Metal => wgpu::Backends::METAL,
            GraphicsBackend::OpenGL => wgpu::Backends::GL,
            GraphicsBackend::WebGPU => wgpu::Backends::BROWSER_WEBGPU,
        }
    }

    /// Get a human-readable description of the backend
    pub fn description(self) -> &'static str {
        match self {
            GraphicsBackend::Auto => "Automatic (best available)",
            GraphicsBackend::Vulkan => "Vulkan (high performance)",
            GraphicsBackend::DirectX12 => "DirectX 12 (Windows)",
            GraphicsBackend::Metal => "Metal (macOS/iOS)",
            GraphicsBackend::OpenGL => "OpenGL ES (compatible)",
            GraphicsBackend::WebGPU => "WebGPU (web)",
        }
    }

    /// Get available backends for the current platform
    pub fn available_backends() -> Vec<GraphicsBackend> {
        let mut backends = vec![GraphicsBackend::Auto];

        #[cfg(target_os = "windows")]
        {
            backends.push(GraphicsBackend::Vulkan);
            backends.push(GraphicsBackend::DirectX12);
            backends.push(GraphicsBackend::OpenGL);
        }

        #[cfg(target_os = "macos")]
        {
            backends.push(GraphicsBackend::Metal);
            backends.push(GraphicsBackend::Vulkan);
            backends.push(GraphicsBackend::OpenGL);
        }

        #[cfg(target_os = "linux")]
        {
            backends.push(GraphicsBackend::Vulkan);
            backends.push(GraphicsBackend::OpenGL);
        }

        #[cfg(target_arch = "wasm32")]
        {
            backends.push(GraphicsBackend::WebGPU);
        }

        backends
    }
}

/// VSync configuration options
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum VSyncMode {
    /// VSync disabled - no frame rate limit (Immediate mode)
    Off,
    /// VSync enabled - frame rate limited to display refresh rate (Fifo mode)
    On,
    /// Adaptive VSync - VSync when possible, immediate when not (Mailbox mode)
    Adaptive,
}

impl Default for VSyncMode {
    fn default() -> Self {
        VSyncMode::On
    }
}

impl VSyncMode {
    /// Convert VSyncMode to wgpu PresentMode, choosing the best available option
    pub fn to_present_mode(self, available_modes: &[wgpu::PresentMode]) -> wgpu::PresentMode {
        match self {
            VSyncMode::Off => {
                // Prefer Immediate, fallback to Mailbox, then Fifo
                available_modes.iter().copied()
                    .find(|&mode| mode == wgpu::PresentMode::Immediate)
                    .or_else(|| available_modes.iter().copied().find(|&mode| mode == wgpu::PresentMode::Mailbox))
                    .unwrap_or(wgpu::PresentMode::Fifo)
            }
            VSyncMode::On => {
                // Prefer Fifo (guaranteed to be available)
                wgpu::PresentMode::Fifo
            }
            VSyncMode::Adaptive => {
                // Prefer Mailbox, fallback to Fifo
                available_modes.iter().copied()
                    .find(|&mode| mode == wgpu::PresentMode::Mailbox)
                    .unwrap_or(wgpu::PresentMode::Fifo)
            }
        }
    }
}

/// CPU Frame Ahead setting for multi-frame pipelining (Phase 3)
/// Supports 0-100 frames ahead for maximum performance testing
#[derive(Debug, Clone, Copy, PartialEq)]
pub struct CpuFrameAhead {
    frames: u32,
}

impl CpuFrameAhead {
    /// Create a new CPU Frame Ahead setting (0-100 frames)
    pub fn new(frames: u32) -> Self {
        let clamped_frames = frames.min(100); // Maximum 100 frames ahead
        if frames > 100 {
            log::warn!("CPU Frame Ahead clamped from {} to 100 (maximum)", frames);
        }
        Self { frames: clamped_frames }
    }

    /// No frame ahead - traditional single-frame rendering
    pub const fn none() -> Self {
        Self { frames: 0 }
    }

    /// 1 frame ahead - balanced performance/latency
    pub const fn one() -> Self {
        Self { frames: 1 }
    }

    /// 2 frames ahead
    pub const fn two() -> Self {
        Self { frames: 2 }
    }

    /// 3 frames ahead
    pub const fn three() -> Self {
        Self { frames: 3 }
    }

    /// Maximum frames ahead (100)
    pub const fn max() -> Self {
        Self { frames: 100 }
    }

    /// Get the number of frames to prepare ahead
    pub fn frame_count(self) -> usize {
        self.frames as usize
    }

    /// Get description for UI
    pub fn description(self) -> String {
        match self.frames {
            0 => "Disabled (Traditional)".to_string(),
            1 => "1 Frame Ahead".to_string(),
            2 => "2 Frames Ahead".to_string(),
            3 => "3 Frames Ahead".to_string(),
            n if n <= 10 => format!("{} Frames Ahead", n),
            n if n <= 50 => format!("{} Frames Ahead (High)", n),
            n => format!("{} Frames Ahead (Extreme)", n),
        }
    }
}

impl Default for CpuFrameAhead {
    fn default() -> Self {
        Self::one() // Default to 1 frame ahead for good performance/latency balance
    }
}

/// Configuration for the renderer
#[derive(Debug, Clone)]
pub struct RendererConfig {
    pub graphics_backend: GraphicsBackend,
    pub vsync_mode: VSyncMode,
    pub desired_maximum_frame_latency: u32,
    /// CPU Frame Ahead setting for multi-frame pipelining
    pub cpu_frame_ahead: CpuFrameAhead,
}

impl Default for RendererConfig {
    fn default() -> Self {
        Self {
            graphics_backend: GraphicsBackend::default(),
            vsync_mode: VSyncMode::default(),
            desired_maximum_frame_latency: 2,
            cpu_frame_ahead: CpuFrameAhead::default(),
        }
    }
}

pub struct Renderer {
    surface: wgpu::Surface<'static>,
    device: Arc<wgpu::Device>,
    queue: Arc<wgpu::Queue>,
    config: wgpu::SurfaceConfiguration,
    size: winit::dpi::PhysicalSize<u32>,
    renderer_config: RendererConfig,
    surface_caps: wgpu::SurfaceCapabilities,
    renderer_3d: Renderer3D,
    simple_frame_pipeline: Option<SimpleFramePipeline>,
    compute_manager: ComputeManager,
    clear_background: bool,
    background_color: wgpu::Color,
    current_backend: GraphicsBackend,

    // Smooth resize system
    pending_resize: Option<winit::dpi::PhysicalSize<u32>>,
    last_resize_time: std::time::Instant,
    resize_debounce_duration: std::time::Duration,

    // VR multiview support
    multiview_supported: bool,
}

impl Renderer {
    pub async fn new(window: Arc<Window>) -> anyhow::Result<Self> {
        Self::new_with_config(window, RendererConfig::default()).await
    }

    pub async fn new_with_config(window: Arc<Window>, renderer_config: RendererConfig) -> anyhow::Result<Self> {
        let size = window.inner_size();

        // Create wgpu instance with selected backend
        let backends = renderer_config.graphics_backend.to_wgpu_backends();
        let instance = wgpu::Instance::new(wgpu::InstanceDescriptor {
            backends,
            dx12_shader_compiler: Default::default(),
            flags: wgpu::InstanceFlags::empty(), // Disable validation layers
            gles_minor_version: wgpu::Gles3MinorVersion::Automatic,
        });

        // Create surface
        let surface = instance.create_surface(window.clone())?;

        // Request adapter
        let adapter = instance
            .request_adapter(&wgpu::RequestAdapterOptions {
                power_preference: wgpu::PowerPreference::default(),
                compatible_surface: Some(&surface),
                force_fallback_adapter: false,
            })
            .await
            .ok_or_else(|| anyhow::anyhow!("Failed to find an appropriate adapter"))?;

        // Request device and queue with multiview support for VR
        let mut required_features = wgpu::Features::empty();

        // Check if multiview is supported (for VR rendering)
        let adapter_features = adapter.features();
        let multiview_supported = adapter_features.contains(wgpu::Features::MULTIVIEW);

        if multiview_supported {
            required_features |= wgpu::Features::MULTIVIEW;
            println!("Multiview support detected - VR rendering will use unified pipeline");
        } else {
            println!("Multiview not supported - VR will use fallback rendering");
        }

        let (device, queue) = adapter
            .request_device(
                &wgpu::DeviceDescriptor {
                    required_features,
                    required_limits: wgpu::Limits::default(),
                    label: None,
                },
                None,
            )
            .await?;

        // Configure surface with better format and mode selection
        let surface_caps = surface.get_capabilities(&adapter);

        // Choose the best surface format
        let surface_format = surface_caps
            .formats
            .iter()
            .copied()
            .find(|f| matches!(f, wgpu::TextureFormat::Bgra8UnormSrgb | wgpu::TextureFormat::Rgba8UnormSrgb))
            .or_else(|| surface_caps.formats.iter().copied().find(|f| f.is_srgb()))
            .unwrap_or(surface_caps.formats[0]);

        // Choose present mode based on VSync configuration
        let present_mode = renderer_config.vsync_mode.to_present_mode(&surface_caps.present_modes);

        // Choose the best alpha mode
        let alpha_mode = surface_caps
            .alpha_modes
            .iter()
            .copied()
            .find(|&mode| mode == wgpu::CompositeAlphaMode::Opaque)
            .unwrap_or(surface_caps.alpha_modes[0]);

        let config = wgpu::SurfaceConfiguration {
            usage: wgpu::TextureUsages::RENDER_ATTACHMENT,
            format: surface_format,
            width: size.width,
            height: size.height,
            present_mode,
            alpha_mode,
            view_formats: vec![],
            desired_maximum_frame_latency: renderer_config.desired_maximum_frame_latency,
        };
        surface.configure(&device, &config);

        // Create shared Arc references for both 3D renderer and parallel renderer
        let device_arc = Arc::new(device);
        let queue_arc = Arc::new(queue);

        // Create 3D renderer
        let renderer_3d = Renderer3D::new(&device_arc, surface_format, size.width, size.height);

        // Create simple frame pipeline for CPU frame ahead functionality
        // Start with 1 frame ahead for testing
        let simple_frame_pipeline = Some(SimpleFramePipeline::new(
            device_arc.clone(),
            queue_arc.clone(),
            renderer_config.cpu_frame_ahead.frame_count(),
        ));

        // Create compute manager for GPU compute shaders
        let compute_manager = ComputeManager::new(device_arc.clone(), queue_arc.clone());

        Ok(Self {
            surface,
            device: device_arc,
            queue: queue_arc,
            config,
            size,
            renderer_config: renderer_config.clone(),
            surface_caps,
            renderer_3d,
            simple_frame_pipeline,
            compute_manager,
            clear_background: true,
            background_color: wgpu::Color {
                r: 0.0,
                g: 0.5,
                b: 0.2,
                a: 1.0,
            },
            current_backend: renderer_config.graphics_backend,

            // Smooth resize system - debounce resize events for 16ms (60 FPS)
            pending_resize: None,
            last_resize_time: std::time::Instant::now(),
            resize_debounce_duration: std::time::Duration::from_millis(16),

            // VR multiview support
            multiview_supported,
        })
    }

    /// Request a resize (debounced for smooth performance during window dragging)
    pub fn request_resize(&mut self, new_size: winit::dpi::PhysicalSize<u32>) {
        if new_size.width > 0 && new_size.height > 0 {
            // Store the pending resize and update timestamp
            self.pending_resize = Some(new_size);
            self.last_resize_time = std::time::Instant::now();

            // Update size immediately for rendering (prevents visual glitches)
            self.size = new_size;
        }
    }

    /// Process pending resize if debounce period has elapsed
    /// Call this every frame to handle smooth resizing
    pub fn update_resize(&mut self) {
        if let Some(new_size) = self.pending_resize {
            let elapsed = self.last_resize_time.elapsed();

            // Only perform expensive resize operations after debounce period
            if elapsed >= self.resize_debounce_duration {
                self.perform_resize(new_size);
                self.pending_resize = None;
            }
        }
    }

    /// Perform the actual expensive resize operations
    fn perform_resize(&mut self, new_size: winit::dpi::PhysicalSize<u32>) {
        // Update surface configuration
        self.config.width = new_size.width;
        self.config.height = new_size.height;
        self.surface.configure(&self.device, &self.config);

        // Resize 3D renderer depth buffer
        self.renderer_3d.resize(&self.device, new_size.width, new_size.height);

    }

    /// Force immediate resize (for compatibility)
    pub fn resize(&mut self, new_size: winit::dpi::PhysicalSize<u32>) {
        if new_size.width > 0 && new_size.height > 0 {
            self.size = new_size;
            self.perform_resize(new_size);
            self.pending_resize = None; // Clear any pending resize
        }
    }



    pub fn render(&mut self) -> Result<(), wgpu::SurfaceError> {
        let output = self.surface.get_current_texture()?;
        let view = output
            .texture
            .create_view(&wgpu::TextureViewDescriptor::default());

        // Choose between CPU frame ahead rendering and immediate rendering
        let use_frame_ahead = self.simple_frame_pipeline.as_ref()
            .map(|pipeline| pipeline.is_enabled())
            .unwrap_or(false);

        if use_frame_ahead {
            self.render_with_frame_ahead(&view)?;
        } else {
            self.render_immediate(&view)?;
        }

        output.present();
        Ok(())
    }

    /// Immediate rendering path (no frame ahead)
    fn render_immediate(&mut self, view: &wgpu::TextureView) -> Result<(), wgpu::SurfaceError> {
        // OPTIMIZATION: Use single command encoder for entire frame
        let mut encoder = self
            .device
            .create_command_encoder(&wgpu::CommandEncoderDescriptor {
                label: Some("Immediate Render Encoder"),
            });

        // OPTIMIZATION: Pass background clear info to 3D renderer to consolidate render passes
        let background_clear = if self.clear_background {
            Some(self.background_color)
        } else {
            None
        };

        // Render everything in consolidated passes
        self.renderer_3d.render_consolidated(&self.device, &self.queue, &mut encoder, view, background_clear);

        // OPTIMIZATION: Single command buffer submission
        self.queue.submit(std::iter::once(encoder.finish()));
        Ok(())
    }

    /// CPU frame ahead rendering path - uses pre-recorded command buffers
    fn render_with_frame_ahead(&mut self, view: &wgpu::TextureView) -> Result<(), wgpu::SurfaceError> {
        // Check if we have a prepared frame (pre-recorded command buffer)
        let has_prepared_frame = self.simple_frame_pipeline.as_ref()
            .map(|pipeline| pipeline.prepared_frame_count() > 0)
            .unwrap_or(false);

        if has_prepared_frame {
            // Use prepared frame - render using pre-calculated state
            if let Some(ref mut frame_pipeline) = self.simple_frame_pipeline {
                if let Some(prepared_frame) = frame_pipeline.get_next_frame() {
                    // Render using the prepared state - faster than calculating from scratch!
                    frame_pipeline.render_prepared_frame(prepared_frame, &mut self.renderer_3d, view);
                }
            }
        } else {
            // No prepared frame available, render immediately
            // This ensures we always have something on screen
            self.render_immediate(view)?;
        }

        // Prepare future frames to keep the CPU ahead of the GPU
        if let Some(ref mut frame_pipeline) = self.simple_frame_pipeline {
            // Prepare as many command buffers as we can up to the limit
            while frame_pipeline.can_prepare_more_frames() {
                let background_clear = if self.clear_background {
                    Some(self.background_color)
                } else {
                    None
                };

                // Prepare rendering state for future frames (proper CPU Frame Ahead!)
                if frame_pipeline.prepare_frame_state(background_clear).is_none() {
                    break; // Failed to prepare frame, stop trying
                }
            }
        }

        Ok(())
    }

    pub fn device(&self) -> &wgpu::Device {
        &self.device
    }

    pub fn queue(&self) -> &wgpu::Queue {
        &self.queue
    }

    pub fn size(&self) -> winit::dpi::PhysicalSize<u32> {
        self.size
    }

    /// Get the current renderer configuration
    pub fn config(&self) -> &RendererConfig {
        &self.renderer_config
    }

    /// Set VSync mode and reconfigure the surface
    pub fn set_vsync_mode(&mut self, vsync_mode: VSyncMode) {
        if self.renderer_config.vsync_mode != vsync_mode {
            self.renderer_config.vsync_mode = vsync_mode;

            // Update present mode based on new VSync setting
            let present_mode = vsync_mode.to_present_mode(&self.surface_caps.present_modes);
            self.config.present_mode = present_mode;

            // Reconfigure the surface with new present mode
            self.surface.configure(&self.device, &self.config);
        }
    }

    /// Get the current VSync mode
    pub fn vsync_mode(&self) -> VSyncMode {
        self.renderer_config.vsync_mode
    }

    /// Set CPU Frame Render Ahead setting
    pub fn set_cpu_frame_ahead(&mut self, cpu_frame_ahead: CpuFrameAhead) {
        if self.renderer_config.cpu_frame_ahead != cpu_frame_ahead {
            self.renderer_config.cpu_frame_ahead = cpu_frame_ahead;

            // Update simple frame pipeline if it exists
            if let Some(ref mut frame_pipeline) = self.simple_frame_pipeline {
                frame_pipeline.set_max_frames_ahead(cpu_frame_ahead.frame_count());
            }
        }
    }

    /// Get the current CPU Frame Render Ahead setting
    pub fn cpu_frame_ahead(&self) -> CpuFrameAhead {
        self.renderer_config.cpu_frame_ahead
    }

    /// Get the current graphics backend
    pub fn graphics_backend(&self) -> GraphicsBackend {
        self.current_backend
    }

    /// Get available graphics backends for the current platform
    pub fn available_backends(&self) -> Vec<GraphicsBackend> {
        GraphicsBackend::available_backends()
    }

    /// Enable or disable CPU frame ahead rendering
    pub fn set_cpu_frame_ahead_enabled(&mut self, enabled: bool) {
        if enabled {
            // Enable with current setting
            let current_setting = self.renderer_config.cpu_frame_ahead;
            self.set_cpu_frame_ahead(current_setting);
        } else {
            // Disable by setting to 0 frames ahead
            self.set_cpu_frame_ahead(CpuFrameAhead::none());
        }
    }

    /// Check if CPU frame ahead rendering is enabled
    pub fn is_cpu_frame_ahead_enabled(&self) -> bool {
        self.renderer_config.cpu_frame_ahead.frame_count() > 0
    }

    /// Get mutable access to the 3D renderer for immediate mode rendering
    pub fn renderer_3d(&mut self) -> &mut Renderer3D {
        &mut self.renderer_3d
    }

    /// Load a mesh into the 3D renderer and return its ID
    pub fn load_mesh(&mut self, mesh: crate::graphics::Mesh) -> u64 {
        self.renderer_3d.load_mesh(&self.device, mesh)
    }

    /// Enable or disable background clearing
    pub fn set_clear_background(&mut self, clear: bool) {
        self.clear_background = clear;
    }

    /// Check if background clearing is enabled
    pub fn is_clear_background(&self) -> bool {
        self.clear_background
    }

    /// Set the background clear color
    pub fn set_background_color(&mut self, color: wgpu::Color) {
        self.background_color = color;
    }

    /// Get the current background color
    pub fn background_color(&self) -> wgpu::Color {
        self.background_color
    }

    /// Switch to a different shader program (for hot-reload testing)
    pub fn switch_shader_program(&mut self, program_name: &str) {
        self.renderer_3d.switch_shader_program(&self.device, self.config.format, program_name);
    }

    /// Check for shader file changes and hot-reload if needed
    pub fn check_shader_updates(&mut self) {
        self.renderer_3d.check_shader_updates(&self.device, self.config.format);

        // Compute shaders are not hot-swappable for better performance
    }

    /// Toggle face culling for debugging sphere winding order
    pub fn toggle_face_culling(&mut self) {
        self.renderer_3d.toggle_face_culling(&self.device, self.config.format);
    }

    /// Get mutable access to the compute manager
    pub fn compute_manager(&mut self) -> &mut ComputeManager {
        &mut self.compute_manager
    }

    /// Get immutable access to the compute manager
    pub fn compute_manager_ref(&self) -> &ComputeManager {
        &self.compute_manager
    }

    /// Create a compute job (handles borrowing internally)
    pub fn create_compute_job<T: bytemuck::Pod + bytemuck::Zeroable>(
        &mut self,
        job_name: &str,
        shader_name: &str,
        shader_filename: &str,
        storage_data: &[T],
        uniform_data: &[u8],
        workgroup_size: (u32, u32, u32),
    ) -> Result<(), String> {
        let shader_manager = self.renderer_3d.shader_manager_mut();
        self.compute_manager.create_job(
            job_name,
            shader_name,
            shader_filename,
            storage_data,
            uniform_data,
            workgroup_size,
            shader_manager,
        )
    }

    /// Execute a compute job (handles borrowing internally)
    pub fn execute_compute_job(&self, job_name: &str) -> Result<(), String> {
        let shader_manager = self.renderer_3d.shader_manager();
        self.compute_manager.execute_job(job_name, shader_manager)
    }

    /// Check if VR multiview rendering is supported
    pub fn supports_vr_multiview(&self) -> bool {
        self.multiview_supported && self.current_backend == GraphicsBackend::Vulkan
    }

    /// Render with VR multiview (desktop + left eye + right eye in single pass)
    pub fn render_vr_multiview(
        &mut self,
        desktop_view_proj: glam::Mat4,
        left_eye_view_proj: glam::Mat4,
        right_eye_view_proj: glam::Mat4,
        vr_targets: &[&wgpu::TextureView],
    ) -> Result<(), wgpu::SurfaceError> {
        let output = self.surface.get_current_texture()?;
        let desktop_view = output
            .texture
            .create_view(&wgpu::TextureViewDescriptor::default());

        // Combine desktop view with VR views
        let mut all_views = vec![&desktop_view];
        all_views.extend_from_slice(vr_targets);

        self.renderer_3d.render_multiview(
            &self.device,
            &self.queue,
            desktop_view_proj,
            left_eye_view_proj,
            right_eye_view_proj,
            &all_views,
            if self.clear_background { Some(self.background_color) } else { None },
        );

        output.present();
        Ok(())
    }


}
