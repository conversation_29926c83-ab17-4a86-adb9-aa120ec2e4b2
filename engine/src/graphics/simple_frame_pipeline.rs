use std::sync::Arc;
use std::collections::VecDeque;
use wgpu::{Device, Queue};
use crate::graphics::renderer_3d::Renderer3D;


/// A prepared frame that's ready for rendering
/// Contains prepared rendering state and data for fast command recording
#[derive(Debug)]
pub struct PreparedFrame {
    pub frame_id: u64,
    pub background_clear: Option<wgpu::Color>,
    // Future: Could contain pre-calculated uniform buffers, instance data, etc.
}

/// Simple frame pipeline for CPU frame ahead (multi-frame pipelining)
/// Prepares future frames while GPU renders current frame
/// This implementation doesn't depend on parallel rendering
pub struct SimpleFramePipeline {
    device: Arc<Device>,
    queue: Arc<Queue>,

    /// Queue of prepared frames ready for rendering
    prepared_frames: VecDeque<PreparedFrame>,

    /// Maximum number of frames to prepare ahead
    max_frames_ahead: usize,

    /// Current frame counter
    current_frame_id: u64,

    /// Whether frame pipelining is enabled
    enabled: bool,
}

impl SimpleFramePipeline {
    /// Create a new simple frame pipeline
    pub fn new(
        device: Arc<Device>,
        queue: Arc<Queue>,
        max_frames_ahead: usize,
    ) -> Self {
        Self {
            device,
            queue,
            prepared_frames: VecDeque::new(),
            max_frames_ahead,
            current_frame_id: 0,
            enabled: max_frames_ahead > 0,
        }
    }

    /// Update the maximum frames ahead setting
    pub fn set_max_frames_ahead(&mut self, max_frames_ahead: usize) {
        self.max_frames_ahead = max_frames_ahead;
        self.enabled = max_frames_ahead > 0;

        // Clear existing prepared frames if disabling or reducing count
        if !self.enabled || self.prepared_frames.len() > max_frames_ahead {
            self.prepared_frames.clear();
        }
    }

    /// Get the maximum frames ahead setting
    pub fn max_frames_ahead(&self) -> usize {
        self.max_frames_ahead
    }

    /// Check if frame pipelining is enabled
    pub fn is_enabled(&self) -> bool {
        self.enabled
    }



    /// Check if we can prepare more frames
    pub fn can_prepare_more_frames(&self) -> bool {
        self.enabled && self.prepared_frames.len() < self.max_frames_ahead
    }

    /// Prepare frame state for future rendering
    /// This is proper CPU Frame Ahead: prepare rendering state now, record commands later
    pub fn prepare_frame_state(
        &mut self,
        background_clear: Option<wgpu::Color>,
    ) -> Option<u64> {
        if !self.enabled || self.prepared_frames.len() >= self.max_frames_ahead {
            return None; // Can't prepare more frames
        }

        let frame_id = self.current_frame_id + self.prepared_frames.len() as u64 + 1;

        // Prepare frame state (this is where we'd prepare uniform buffers, instance data, etc.)
        // For now, we just store the clear color, but this could be expanded to:
        // - Pre-calculate uniform buffer data
        // - Prepare instance buffer data
        // - Sort draw calls for optimal rendering
        // - Calculate frustum culling results
        let prepared_frame = PreparedFrame {
            frame_id,
            background_clear,
        };

        self.prepared_frames.push_back(prepared_frame);

        Some(frame_id)
    }

    /// Get the next prepared frame for rendering
    pub fn get_next_frame(&mut self) -> Option<PreparedFrame> {
        if let Some(frame) = self.prepared_frames.pop_front() {
            self.current_frame_id = frame.frame_id;
            Some(frame)
        } else {
            None
        }
    }

    /// Render a prepared frame using its pre-calculated state
    /// This is where the prepared state gets turned into actual rendering
    pub fn render_prepared_frame(
        &self,
        prepared_frame: PreparedFrame,
        renderer_3d: &mut Renderer3D,
        surface_view: &wgpu::TextureView,
    ) {
        // Use the prepared state to render quickly
        // The CPU Frame Ahead benefit comes from having the state pre-calculated
        let mut encoder = self.device.create_command_encoder(&wgpu::CommandEncoderDescriptor {
            label: Some(&format!("CPU Frame Ahead Render {}", prepared_frame.frame_id)),
        });

        // Render using the prepared state
        renderer_3d.render_consolidated(&self.device, &self.queue, &mut encoder, surface_view, prepared_frame.background_clear);

        // Submit the command buffer
        self.queue.submit(std::iter::once(encoder.finish()));
    }



    /// Get the number of frames currently prepared
    pub fn prepared_frame_count(&self) -> usize {
        self.prepared_frames.len()
    }

    /// Get the current frame ID
    pub fn current_frame_id(&self) -> u64 {
        self.current_frame_id
    }

    /// Clear all prepared frames (useful when scene changes dramatically)
    pub fn clear_prepared_frames(&mut self) {
        let count = self.prepared_frames.len();
        self.prepared_frames.clear();
        if count > 0 {
            log::debug!("Cleared {} prepared frames from pipeline", count);
        }
    }



    /// Get frame pipeline statistics
    pub fn stats(&self) -> SimpleFramePipelineStats {
        SimpleFramePipelineStats {
            enabled: self.enabled,
            max_frames_ahead: self.max_frames_ahead,
            prepared_frames: self.prepared_frames.len(),
            current_frame_id: self.current_frame_id,
        }
    }
}

/// Statistics about the simple frame pipeline
#[derive(Debug, Clone)]
pub struct SimpleFramePipelineStats {
    pub enabled: bool,
    pub max_frames_ahead: usize,
    pub prepared_frames: usize,
    pub current_frame_id: u64,
}

impl std::fmt::Display for SimpleFramePipelineStats {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        if self.enabled {
            write!(f, "CPU Frame Ahead: {}/{} frames prepared (current: {})",
                   self.prepared_frames, self.max_frames_ahead, self.current_frame_id)
        } else {
            write!(f, "CPU Frame Ahead: Disabled")
        }
    }
}
