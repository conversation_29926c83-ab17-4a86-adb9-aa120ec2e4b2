use wgpu::util::DeviceExt;
use glam::Mat4;
use std::collections::HashMap;
use crate::graphics::{mesh::Mesh, shader};
use crate::log_info;
use crate::assets::ShaderManager;
use crate::log_debug;

/// Camera uniform data (view-projection matrix)
#[repr(C)]
#[derive(Debug, Co<PERSON>, Clone, bytemuck::Pod, bytemuck::Zeroable)]
pub struct CameraUniform {
    pub view_proj: [[f32; 4]; 4],
}

impl CameraUniform {
    pub fn new() -> Self {
        Self {
            view_proj: Mat4::IDENTITY.to_cols_array_2d(),
        }
    }

    pub fn update_view_proj(&mut self, view_proj: Mat4) {
        self.view_proj = view_proj.to_cols_array_2d();
    }
}

/// VR multiview camera uniform data (3 view-projection matrices)
#[repr(C)]
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, bytemuck::Pod, bytemuck::Zeroable)]
pub struct VrCameraUniform {
    pub left_eye_view_proj: [[f32; 4]; 4],
    pub right_eye_view_proj: [[f32; 4]; 4],
    pub desktop_view_proj: [[f32; 4]; 4],
}

impl VrCameraUniform {
    pub fn new() -> Self {
        Self {
            left_eye_view_proj: Mat4::IDENTITY.to_cols_array_2d(),
            right_eye_view_proj: Mat4::IDENTITY.to_cols_array_2d(),
            desktop_view_proj: Mat4::IDENTITY.to_cols_array_2d(),
        }
    }

    pub fn update_matrices(&mut self, left_eye: Mat4, right_eye: Mat4, desktop: Mat4) {
        self.left_eye_view_proj = left_eye.to_cols_array_2d();
        self.right_eye_view_proj = right_eye.to_cols_array_2d();
        self.desktop_view_proj = desktop.to_cols_array_2d();
    }
}

/// Model uniform data (model matrix)
#[repr(C)]
#[derive(Debug, Copy, Clone, bytemuck::Pod, bytemuck::Zeroable)]
pub struct ModelUniform {
    pub model: [[f32; 4]; 4],
}

impl ModelUniform {
    pub fn new() -> Self {
        Self {
            model: Mat4::IDENTITY.to_cols_array_2d(),
        }
    }

    pub fn update_model(&mut self, model: Mat4) {
        self.model = model.to_cols_array_2d();
    }
}

/// Draw command for immediate mode rendering
#[derive(Debug, Clone)]
pub struct DrawCommand {
    pub mesh_id: u64,
    pub transform: Mat4,
}

/// Instance data for instanced rendering
#[repr(C)]
#[derive(Debug, Copy, Clone, bytemuck::Pod, bytemuck::Zeroable)]
pub struct InstanceData {
    pub transform: [[f32; 4]; 4],
}

impl InstanceData {
    pub fn new(transform: Mat4) -> Self {
        Self {
            transform: transform.to_cols_array_2d(),
        }
    }
}

/// Smart batching system for optimal rendering
#[derive(Debug)]
pub struct RenderBatch {
    pub mesh_id: u64,
    pub transforms: Vec<Mat4>,
    pub batch_type: BatchType,
}

#[derive(Debug)]
pub enum BatchType {
    /// Use instanced rendering (3+ identical objects)
    Instanced { count: usize },
    /// Use dynamic uniform buffers (1-2 objects)
    DynamicUniform { count: usize },
}

impl RenderBatch {
    pub fn new(mesh_id: u64, transform: Mat4) -> Self {
        Self {
            mesh_id,
            transforms: vec![transform],
            batch_type: BatchType::DynamicUniform { count: 1 },
        }
    }

    pub fn add_transform(&mut self, transform: Mat4) {
        self.transforms.push(transform);
        let count = self.transforms.len();

        // Auto-detect optimal batch type based on count
        self.batch_type = if count >= 3 {
            BatchType::Instanced { count }
        } else {
            BatchType::DynamicUniform { count }
        };
    }

    pub fn should_use_instancing(&self) -> bool {
        matches!(self.batch_type, BatchType::Instanced { .. })
    }
}

/// 3D Renderer with immediate mode API
pub struct Renderer3D {
    pipeline: wgpu::RenderPipeline,
    depth_texture: wgpu::Texture,
    depth_view: wgpu::TextureView,
    camera_uniform: CameraUniform,
    camera_buffer: wgpu::Buffer,
    camera_bind_group: wgpu::BindGroup,
    camera_bind_group_layout: wgpu::BindGroupLayout,
    model_buffer_pool: Vec<wgpu::Buffer>,
    model_bind_group_pool: Vec<wgpu::BindGroup>,
    objects_per_buffer: usize,
    model_uniform_size: u64,
    buffer_bind_group_layout: wgpu::BindGroupLayout,
    instance_buffers: HashMap<u64, wgpu::Buffer>,
    dummy_instance_buffer: wgpu::Buffer,
    max_instances_per_mesh: usize,
    camera_dirty: bool,
    model_dirty: bool,
    meshes: HashMap<u64, Mesh>,
    next_mesh_id: u64,
    draw_commands: Vec<DrawCommand>,
    width: u32,
    height: u32,
    clear_depth: bool,
    shader_manager: ShaderManager,
    current_shader_program: String,

    // VR multiview support
    multiview_pipeline: Option<wgpu::RenderPipeline>,
    vr_camera_uniform: VrCameraUniform,
    vr_camera_buffer: wgpu::Buffer,
    vr_camera_bind_group: wgpu::BindGroup,
    vr_camera_bind_group_layout: wgpu::BindGroupLayout,
    vr_camera_dirty: bool,
}

impl Renderer3D {
    pub fn new(device: &std::sync::Arc<wgpu::Device>, surface_format: wgpu::TextureFormat, width: u32, height: u32) -> Self {
        Self::new_with_profiler(device, surface_format, width, height, None)
    }

    pub fn new_with_profiler(device: &std::sync::Arc<wgpu::Device>, surface_format: wgpu::TextureFormat, width: u32, height: u32, profiler: Option<&mut crate::profiler::Profiler>) -> Self {
        // Create depth texture
        let (depth_texture, depth_view) = Self::create_depth_texture(device, width, height);

        // Track depth texture VRAM usage
        if let Some(profiler) = profiler {
            // Depth texture: 32-bit depth format, so 4 bytes per pixel
            let depth_texture_size = (width * height * 4) as u64;
            profiler.add_texture_memory(depth_texture_size);
        }

        // Create camera uniform buffer
        let camera_uniform = CameraUniform::new();
        let camera_buffer = device.create_buffer_init(&wgpu::util::BufferInitDescriptor {
            label: Some("Camera Buffer"),
            contents: bytemuck::cast_slice(&[camera_uniform]),
            usage: wgpu::BufferUsages::UNIFORM | wgpu::BufferUsages::COPY_DST,
        });

        // Create dynamic model uniform buffer (can hold multiple model matrices)
        let model_uniform_size = std::mem::size_of::<ModelUniform>() as u64;

        // Ensure proper alignment for dynamic uniform buffers
        let min_uniform_buffer_offset_alignment = device.limits().min_uniform_buffer_offset_alignment as u64;
        let aligned_model_uniform_size = {
            let remainder = model_uniform_size % min_uniform_buffer_offset_alignment;
            if remainder != 0 {
                model_uniform_size + min_uniform_buffer_offset_alignment - remainder
            } else {
                model_uniform_size
            }
        };

        // GPU-adaptive buffer sizing: guarantee 16KB minimum, use more if GPU supports it
        let max_uniform_buffer_binding_size = device.limits().max_uniform_buffer_binding_size as u64;
        let guaranteed_16kb = 16 * 1024; // 16KB minimum for old GPUs (15+ years)

        // Calculate objects per buffer based on GPU capability
        let min_objects_16kb = (guaranteed_16kb / aligned_model_uniform_size) as usize;
        let max_objects_gpu_limit = (max_uniform_buffer_binding_size / aligned_model_uniform_size) as usize;

        // Use the larger buffer size if GPU supports more than 16KB
        let objects_per_buffer = if max_uniform_buffer_binding_size > guaranteed_16kb {
            max_objects_gpu_limit // Use full GPU capacity per buffer
        } else {
            min_objects_16kb // Fallback to 16KB for old GPUs
        };

        println!("GPU-ADAPTIVE DYNAMIC BUFFER SYSTEM:");
        println!("  16KB minimum: {} objects per buffer", min_objects_16kb);
        println!("  GPU maximum: {} objects per buffer ({:.1} KB)",
                 max_objects_gpu_limit,
                 max_uniform_buffer_binding_size as f64 / 1024.0);
        println!("  Selected: {} objects per buffer", objects_per_buffer);

        // Start with empty buffer pools - they will be allocated dynamically
        let model_buffer_pool = Vec::new();
        let model_bind_group_pool = Vec::new();

        // Create bind group layouts
        let camera_bind_group_layout = device.create_bind_group_layout(&wgpu::BindGroupLayoutDescriptor {
            entries: &[wgpu::BindGroupLayoutEntry {
                binding: 0,
                visibility: wgpu::ShaderStages::VERTEX,
                ty: wgpu::BindingType::Buffer {
                    ty: wgpu::BufferBindingType::Uniform,
                    has_dynamic_offset: false,
                    min_binding_size: None,
                },
                count: None,
            }],
            label: Some("Camera Bind Group Layout"),
        });

        let model_bind_group_layout = device.create_bind_group_layout(&wgpu::BindGroupLayoutDescriptor {
            entries: &[wgpu::BindGroupLayoutEntry {
                binding: 0,
                visibility: wgpu::ShaderStages::VERTEX,
                ty: wgpu::BindingType::Buffer {
                    ty: wgpu::BufferBindingType::Uniform,
                    has_dynamic_offset: true, // Enable dynamic offsets for multiple objects
                    min_binding_size: Some(wgpu::BufferSize::new(model_uniform_size).unwrap()),
                },
                count: None,
            }],
            label: Some("Model Bind Group Layout"),
        });

        // Create bind groups
        let camera_bind_group = device.create_bind_group(&wgpu::BindGroupDescriptor {
            layout: &camera_bind_group_layout,
            entries: &[wgpu::BindGroupEntry {
                binding: 0,
                resource: camera_buffer.as_entire_binding(),
            }],
            label: Some("Camera Bind Group"),
        });

        // Create shader manager and load shaders
        let mut shader_manager = ShaderManager::new(device.clone(), "shaders");

        // Register error shader first (critical for fallback)
        if let Err(e) = shader_manager.register_error_shader() {
            println!("WARNING: Failed to load error shader: {}", e);
        }

        // Register shader programs
        if let Err(e) = shader_manager.register_shader_program("basic", "basic.vert.wgsl", "basic.frag.wgsl") {
            println!("WARNING: Failed to load basic shaders: {}", e);
        }

        // Register the random color shader program for testing
        if let Err(e) = shader_manager.register_shader_program("random_color", "basic.vert.wgsl", "random_color.frag.wgsl") {
            println!("WARNING: Failed to load random_color shaders: {}", e);
        }

        // Register multiview shader program for VR
        if let Err(e) = shader_manager.register_shader_program("multiview", "multiview.vert.wgsl", "multiview.frag.wgsl") {
            println!("WARNING: Failed to load multiview shaders: {}", e);
        }

        let pipeline = if shader_manager.has_shader_program("basic") {
            // Create render pipeline using shader manager
            Self::create_pipeline_from_shader_manager(
                device,
                surface_format,
                &camera_bind_group_layout,
                &model_bind_group_layout,
                &shader_manager,
                "basic", // Start with basic shader
            )
        } else {
            // Fall back to hardcoded shaders
            shader::create_3d_pipeline(
                device,
                surface_format,
                &camera_bind_group_layout,
                &model_bind_group_layout,
            )
        };

        // Create VR camera bind group layout
        let vr_camera_bind_group_layout = device.create_bind_group_layout(&wgpu::BindGroupLayoutDescriptor {
            entries: &[
                wgpu::BindGroupLayoutEntry {
                    binding: 0,
                    visibility: wgpu::ShaderStages::VERTEX,
                    ty: wgpu::BindingType::Buffer {
                        ty: wgpu::BufferBindingType::Uniform,
                        has_dynamic_offset: false,
                        min_binding_size: None,
                    },
                    count: None,
                },
                wgpu::BindGroupLayoutEntry {
                    binding: 1,
                    visibility: wgpu::ShaderStages::VERTEX,
                    ty: wgpu::BindingType::Buffer {
                        ty: wgpu::BufferBindingType::Uniform,
                        has_dynamic_offset: false,
                        min_binding_size: None,
                    },
                    count: None,
                },
            ],
            label: Some("VR Camera Bind Group Layout"),
        });

        // Create VR camera uniform and buffer
        let vr_camera_uniform = VrCameraUniform::new();
        let vr_camera_buffer = device.create_buffer_init(&wgpu::util::BufferInitDescriptor {
            label: Some("VR Camera Buffer"),
            contents: bytemuck::cast_slice(&[vr_camera_uniform]),
            usage: wgpu::BufferUsages::UNIFORM | wgpu::BufferUsages::COPY_DST,
        });

        // Create VR camera bind group
        let vr_camera_bind_group = device.create_bind_group(&wgpu::BindGroupDescriptor {
            layout: &vr_camera_bind_group_layout,
            entries: &[
                wgpu::BindGroupEntry {
                    binding: 0,
                    resource: camera_buffer.as_entire_binding(),
                },
                wgpu::BindGroupEntry {
                    binding: 1,
                    resource: vr_camera_buffer.as_entire_binding(),
                },
            ],
            label: Some("VR Camera Bind Group"),
        });

        // Create multiview pipeline if multiview is supported (before moving model_bind_group_layout)
        let multiview_pipeline = if device.features().contains(wgpu::Features::MULTIVIEW) && shader_manager.has_shader_program("multiview") {
            println!("Creating multiview pipeline for VR rendering");
            // Use Bgra8UnormSrgb format to match VR render pass
            Some(Self::create_multiview_pipeline(
                device,
                wgpu::TextureFormat::Bgra8UnormSrgb, // Fixed format for VR
                &vr_camera_bind_group_layout,
                &model_bind_group_layout,
                &shader_manager,
            ))
        } else {
            println!("Multiview not supported or shaders not available - VR will be disabled");
            None
        };

        // Store the bind group layout for dynamic buffer creation (after multiview pipeline creation)
        let buffer_bind_group_layout = model_bind_group_layout;

        // Create dummy instance buffer for dynamic uniform rendering (zero-filled)
        let dummy_instance_data = vec![0u8; std::mem::size_of::<InstanceData>()];
        let dummy_instance_buffer = device.create_buffer_init(&wgpu::util::BufferInitDescriptor {
            label: Some("Dummy Instance Buffer"),
            contents: &dummy_instance_data,
            usage: wgpu::BufferUsages::VERTEX,
        });

        Self {
            pipeline,
            depth_texture,
            depth_view,
            camera_uniform,
            camera_buffer,
            camera_bind_group,
            camera_bind_group_layout,
            model_buffer_pool,
            model_bind_group_pool,
            objects_per_buffer,
            model_uniform_size: aligned_model_uniform_size,
            buffer_bind_group_layout,
            instance_buffers: HashMap::new(),
            dummy_instance_buffer,
            max_instances_per_mesh: 10000,
            camera_dirty: true,
            model_dirty: true,
            meshes: HashMap::new(),
            next_mesh_id: 1,
            draw_commands: Vec::new(),
            width,
            height,
            clear_depth: true,
            shader_manager,
            current_shader_program: "basic".to_string(),

            // VR multiview support
            multiview_pipeline,
            vr_camera_uniform,
            vr_camera_buffer,
            vr_camera_bind_group,
            vr_camera_bind_group_layout,
            vr_camera_dirty: true,
        }
    }

    /// Create a render pipeline with configurable culling (for debugging)
    fn create_pipeline_with_culling(
        device: &wgpu::Device,
        surface_format: wgpu::TextureFormat,
        camera_bind_group_layout: &wgpu::BindGroupLayout,
        model_bind_group_layout: &wgpu::BindGroupLayout,
        shader_manager: &ShaderManager,
        program_name: &str,
        cull_mode: Option<wgpu::Face>,
    ) -> wgpu::RenderPipeline {
        // Get vertex and fragment shaders from shader manager
        let vertex_shader = shader_manager.get_vertex_shader(program_name)
            .expect(&format!("{} vertex shader should be loaded", program_name));
        let fragment_shader = shader_manager.get_fragment_shader(program_name)
            .expect(&format!("{} fragment shader should be loaded", program_name));

        let pipeline_layout = device.create_pipeline_layout(&wgpu::PipelineLayoutDescriptor {
            label: Some("3D Pipeline Layout (Configurable Culling)"),
            bind_group_layouts: &[camera_bind_group_layout, model_bind_group_layout],
            push_constant_ranges: &[],
        });

        device.create_render_pipeline(&wgpu::RenderPipelineDescriptor {
            label: Some("3D Render Pipeline (Configurable Culling)"),
            layout: Some(&pipeline_layout),
            vertex: wgpu::VertexState {
                module: vertex_shader,
                entry_point: "vs_main",
                buffers: &[
                    crate::graphics::mesh::Vertex::desc(),
                    // Instance buffer layout
                    wgpu::VertexBufferLayout {
                        array_stride: std::mem::size_of::<crate::graphics::renderer_3d::InstanceData>() as wgpu::BufferAddress,
                        step_mode: wgpu::VertexStepMode::Instance,
                        attributes: &[
                            // Instance matrix (4 vec4s)
                            wgpu::VertexAttribute {
                                offset: 0,
                                shader_location: 3,
                                format: wgpu::VertexFormat::Float32x4,
                            },
                            wgpu::VertexAttribute {
                                offset: std::mem::size_of::<[f32; 4]>() as wgpu::BufferAddress,
                                shader_location: 4,
                                format: wgpu::VertexFormat::Float32x4,
                            },
                            wgpu::VertexAttribute {
                                offset: std::mem::size_of::<[f32; 8]>() as wgpu::BufferAddress,
                                shader_location: 5,
                                format: wgpu::VertexFormat::Float32x4,
                            },
                            wgpu::VertexAttribute {
                                offset: std::mem::size_of::<[f32; 12]>() as wgpu::BufferAddress,
                                shader_location: 6,
                                format: wgpu::VertexFormat::Float32x4,
                            },
                        ],
                    },
                ],
            },
            fragment: Some(wgpu::FragmentState {
                module: fragment_shader,
                entry_point: "fs_main",
                targets: &[Some(wgpu::ColorTargetState {
                    format: surface_format,
                    blend: Some(wgpu::BlendState::REPLACE),
                    write_mask: wgpu::ColorWrites::ALL,
                })],
            }),
            primitive: wgpu::PrimitiveState {
                topology: wgpu::PrimitiveTopology::TriangleList,
                strip_index_format: None,
                front_face: wgpu::FrontFace::Ccw,
                cull_mode, // Configurable culling!
                polygon_mode: wgpu::PolygonMode::Fill,
                unclipped_depth: false,
                conservative: false,
            },
            depth_stencil: Some(wgpu::DepthStencilState {
                format: wgpu::TextureFormat::Depth32Float,
                depth_write_enabled: true,
                depth_compare: wgpu::CompareFunction::Less,
                stencil: wgpu::StencilState::default(),
                bias: wgpu::DepthBiasState::default(),
            }),
            multisample: wgpu::MultisampleState {
                count: 1,
                mask: !0,
                alpha_to_coverage_enabled: false,
            },
            multiview: None,
        })
    }

    /// Create a render pipeline using the shader manager
    fn create_pipeline_from_shader_manager(
        device: &wgpu::Device,
        surface_format: wgpu::TextureFormat,
        camera_bind_group_layout: &wgpu::BindGroupLayout,
        model_bind_group_layout: &wgpu::BindGroupLayout,
        shader_manager: &ShaderManager,
        program_name: &str,
    ) -> wgpu::RenderPipeline {
        // Get vertex and fragment shaders from shader manager
        let vertex_shader = shader_manager.get_vertex_shader(program_name)
            .expect(&format!("{} vertex shader should be loaded", program_name));
        let fragment_shader = shader_manager.get_fragment_shader(program_name)
            .expect(&format!("{} fragment shader should be loaded", program_name));

        let pipeline_layout = device.create_pipeline_layout(&wgpu::PipelineLayoutDescriptor {
            label: Some("3D Pipeline Layout (External Shaders)"),
            bind_group_layouts: &[camera_bind_group_layout, model_bind_group_layout],
            push_constant_ranges: &[],
        });

        device.create_render_pipeline(&wgpu::RenderPipelineDescriptor {
            label: Some("3D Render Pipeline (External Shaders)"),
            layout: Some(&pipeline_layout),
            vertex: wgpu::VertexState {
                module: vertex_shader,
                entry_point: "vs_main",
                buffers: &[
                    crate::graphics::mesh::Vertex::desc(),
                    // Instance buffer layout
                    wgpu::VertexBufferLayout {
                        array_stride: std::mem::size_of::<crate::graphics::renderer_3d::InstanceData>() as wgpu::BufferAddress,
                        step_mode: wgpu::VertexStepMode::Instance,
                        attributes: &[
                            // Instance matrix (4 vec4s)
                            wgpu::VertexAttribute {
                                offset: 0,
                                shader_location: 3,
                                format: wgpu::VertexFormat::Float32x4,
                            },
                            wgpu::VertexAttribute {
                                offset: std::mem::size_of::<[f32; 4]>() as wgpu::BufferAddress,
                                shader_location: 4,
                                format: wgpu::VertexFormat::Float32x4,
                            },
                            wgpu::VertexAttribute {
                                offset: (2 * std::mem::size_of::<[f32; 4]>()) as wgpu::BufferAddress,
                                shader_location: 5,
                                format: wgpu::VertexFormat::Float32x4,
                            },
                            wgpu::VertexAttribute {
                                offset: (3 * std::mem::size_of::<[f32; 4]>()) as wgpu::BufferAddress,
                                shader_location: 6,
                                format: wgpu::VertexFormat::Float32x4,
                            },
                        ],
                    },
                ],
            },
            fragment: Some(wgpu::FragmentState {
                module: fragment_shader,
                entry_point: "fs_main",
                targets: &[Some(wgpu::ColorTargetState {
                    format: surface_format,
                    blend: Some(wgpu::BlendState::REPLACE),
                    write_mask: wgpu::ColorWrites::ALL,
                })],
            }),
            primitive: wgpu::PrimitiveState {
                topology: wgpu::PrimitiveTopology::TriangleList,
                strip_index_format: None,
                front_face: wgpu::FrontFace::Ccw,
                cull_mode: Some(wgpu::Face::Back),
                polygon_mode: wgpu::PolygonMode::Fill,
                unclipped_depth: false,
                conservative: false,
            },
            depth_stencil: Some(wgpu::DepthStencilState {
                format: wgpu::TextureFormat::Depth32Float,
                depth_write_enabled: true,
                depth_compare: wgpu::CompareFunction::Less,
                stencil: wgpu::StencilState::default(),
                bias: wgpu::DepthBiasState::default(),
            }),
            multisample: wgpu::MultisampleState {
                count: 1,
                mask: !0,
                alpha_to_coverage_enabled: false,
            },
            multiview: None,
        })
    }

    /// Create a multiview render pipeline for VR stereo rendering
    fn create_multiview_pipeline(
        device: &wgpu::Device,
        surface_format: wgpu::TextureFormat,
        vr_camera_bind_group_layout: &wgpu::BindGroupLayout,
        model_bind_group_layout: &wgpu::BindGroupLayout,
        shader_manager: &ShaderManager,
    ) -> wgpu::RenderPipeline {
        // Get vertex and fragment shaders from shader manager
        let vertex_shader = shader_manager.get_vertex_shader("multiview")
            .expect("multiview vertex shader should be loaded");
        let fragment_shader = shader_manager.get_fragment_shader("multiview")
            .expect("multiview fragment shader should be loaded");

        let pipeline_layout = device.create_pipeline_layout(&wgpu::PipelineLayoutDescriptor {
            label: Some("Multiview Pipeline Layout"),
            bind_group_layouts: &[vr_camera_bind_group_layout, model_bind_group_layout],
            push_constant_ranges: &[],
        });

        device.create_render_pipeline(&wgpu::RenderPipelineDescriptor {
            label: Some("Multiview Render Pipeline"),
            layout: Some(&pipeline_layout),
            vertex: wgpu::VertexState {
                module: vertex_shader,
                entry_point: "vs_main",
                buffers: &[
                    crate::graphics::mesh::Vertex::desc(),
                    // Instance buffer layout
                    wgpu::VertexBufferLayout {
                        array_stride: std::mem::size_of::<crate::graphics::renderer_3d::InstanceData>() as wgpu::BufferAddress,
                        step_mode: wgpu::VertexStepMode::Instance,
                        attributes: &[
                            // Instance matrix (4 vec4s)
                            wgpu::VertexAttribute {
                                offset: 0,
                                shader_location: 3,
                                format: wgpu::VertexFormat::Float32x4,
                            },
                            wgpu::VertexAttribute {
                                offset: std::mem::size_of::<[f32; 4]>() as wgpu::BufferAddress,
                                shader_location: 4,
                                format: wgpu::VertexFormat::Float32x4,
                            },
                            wgpu::VertexAttribute {
                                offset: (2 * std::mem::size_of::<[f32; 4]>()) as wgpu::BufferAddress,
                                shader_location: 5,
                                format: wgpu::VertexFormat::Float32x4,
                            },
                            wgpu::VertexAttribute {
                                offset: (3 * std::mem::size_of::<[f32; 4]>()) as wgpu::BufferAddress,
                                shader_location: 6,
                                format: wgpu::VertexFormat::Float32x4,
                            },
                        ],
                    },
                ],
            },
            fragment: Some(wgpu::FragmentState {
                module: fragment_shader,
                entry_point: "fs_main",
                targets: &[Some(wgpu::ColorTargetState {
                    format: surface_format,
                    blend: Some(wgpu::BlendState::REPLACE),
                    write_mask: wgpu::ColorWrites::ALL,
                })],
            }),
            primitive: wgpu::PrimitiveState {
                topology: wgpu::PrimitiveTopology::TriangleList,
                strip_index_format: None,
                front_face: wgpu::FrontFace::Ccw,
                cull_mode: Some(wgpu::Face::Back),
                polygon_mode: wgpu::PolygonMode::Fill,
                unclipped_depth: false,
                conservative: false,
            },
            depth_stencil: Some(wgpu::DepthStencilState {
                format: wgpu::TextureFormat::Depth32Float,
                depth_write_enabled: true,
                depth_compare: wgpu::CompareFunction::Less,
                stencil: wgpu::StencilState::default(),
                bias: wgpu::DepthBiasState::default(),
            }),
            multisample: wgpu::MultisampleState {
                count: 1,
                mask: !0,
                alpha_to_coverage_enabled: false,
            },
            multiview: Some(std::num::NonZeroU32::new(3).unwrap()), // 3 views: left eye, right eye, desktop
        })
    }

    fn create_depth_texture(device: &wgpu::Device, width: u32, height: u32) -> (wgpu::Texture, wgpu::TextureView) {
        let depth_texture = device.create_texture(&wgpu::TextureDescriptor {
            label: Some("Depth Texture (Clear: 1.0)"),
            size: wgpu::Extent3d {
                width,
                height,
                depth_or_array_layers: 1,
            },
            mip_level_count: 1,
            sample_count: 1,
            dimension: wgpu::TextureDimension::D2,
            format: wgpu::TextureFormat::Depth32Float,
            usage: wgpu::TextureUsages::RENDER_ATTACHMENT | wgpu::TextureUsages::TEXTURE_BINDING,
            view_formats: &[],
        });

        let depth_view = depth_texture.create_view(&wgpu::TextureViewDescriptor::default());

        (depth_texture, depth_view)
    }

    /// Resize the depth buffer when window size changes
    pub fn resize(&mut self, device: &wgpu::Device, width: u32, height: u32) {
        if self.width != width || self.height != height {
            self.width = width;
            self.height = height;
            let (depth_texture, depth_view) = Self::create_depth_texture(device, width, height);
            self.depth_texture = depth_texture;
            self.depth_view = depth_view;
        }
    }

    /// Load a mesh and return its ID for drawing
    pub fn load_mesh(&mut self, device: &wgpu::Device, mut mesh: Mesh) -> u64 {
        mesh.upload_to_gpu(device);
        let id = self.next_mesh_id;
        self.meshes.insert(id, mesh);
        self.next_mesh_id += 1;
        id
    }

    /// Load a mesh with VRAM tracking and return its ID for drawing
    pub fn load_mesh_with_profiler(&mut self, device: &wgpu::Device, mut mesh: Mesh, profiler: &mut crate::profiler::Profiler) -> u64 {
        mesh.upload_to_gpu_with_profiler(device, Some(profiler));
        let id = self.next_mesh_id;
        self.meshes.insert(id, mesh);
        self.next_mesh_id += 1;
        id
    }

    /// Load an already uploaded mesh (for when VRAM tracking is done externally)
    pub fn load_uploaded_mesh(&mut self, mesh: Mesh) -> u64 {
        let id = self.next_mesh_id;
        self.meshes.insert(id, mesh);
        self.next_mesh_id += 1;
        id
    }

    /// Set the camera view-projection matrix
    pub fn set_camera(&mut self, view_proj: Mat4) {
        let old_view_proj = self.camera_uniform.view_proj;
        self.camera_uniform.update_view_proj(view_proj);
        if old_view_proj != self.camera_uniform.view_proj {
            self.camera_dirty = true;
        }
    }

    /// Clear the frame (call this at the start of each frame)
    pub fn clear_frame(&mut self) {
        self.draw_commands.clear();
    }

    /// Clear the background with the specified color (immediate mode)
    pub fn clear_background(&mut self,
                           _queue: &wgpu::Queue,
                           encoder: &mut wgpu::CommandEncoder,
                           color_view: &wgpu::TextureView,
                           color: [f32; 4]) {
        let _render_pass = encoder.begin_render_pass(&wgpu::RenderPassDescriptor {
            label: Some("Background Clear Pass"),
            color_attachments: &[Some(wgpu::RenderPassColorAttachment {
                view: color_view,
                resolve_target: None,
                ops: wgpu::Operations {
                    load: wgpu::LoadOp::Clear(wgpu::Color {
                        r: color[0] as f64,
                        g: color[1] as f64,
                        b: color[2] as f64,
                        a: color[3] as f64,
                    }),
                    store: wgpu::StoreOp::Store,
                },
            })],
            depth_stencil_attachment: None,
            occlusion_query_set: None,
            timestamp_writes: None,
        });
    }

    /// Clear the depth buffer (immediate mode)
    pub fn clear_depth_buffer(&mut self,
                             _queue: &wgpu::Queue,
                             encoder: &mut wgpu::CommandEncoder,
                             color_view: &wgpu::TextureView) {
        let _render_pass = encoder.begin_render_pass(&wgpu::RenderPassDescriptor {
            label: Some("Depth Clear Pass"),
            color_attachments: &[Some(wgpu::RenderPassColorAttachment {
                view: color_view,
                resolve_target: None,
                ops: wgpu::Operations {
                    load: wgpu::LoadOp::Load, // Don't clear color
                    store: wgpu::StoreOp::Store,
                },
            })],
            depth_stencil_attachment: Some(wgpu::RenderPassDepthStencilAttachment {
                view: &self.depth_view,
                depth_ops: Some(wgpu::Operations {
                    load: wgpu::LoadOp::Clear(1.0), // Clear depth to 1.0
                    store: wgpu::StoreOp::Store,
                }),
                stencil_ops: None,
            }),
            occlusion_query_set: None,
            timestamp_writes: None,
        });
    }

    /// Draw a mesh with the given transform
    pub fn draw_mesh(&mut self, mesh_id: u64, transform: Mat4) {
        if self.meshes.contains_key(&mesh_id) {
            self.draw_commands.push(DrawCommand {
                mesh_id,
                transform,
            });
            // Mark model as dirty since we have new draw commands
            self.model_dirty = true;
        }
    }



    /// Enable or disable depth buffer clearing
    pub fn set_clear_depth(&mut self, clear: bool) {
        self.clear_depth = clear;
    }

    /// Check if depth buffer clearing is enabled
    pub fn is_clear_depth(&self) -> bool {
        self.clear_depth
    }

    /// Extract current scene data for VR rendering
    pub fn get_scene_data_for_vr(&self) -> crate::vr::VrSceneData {
        crate::vr::VrSceneData {
            view_projection: glam::Mat4::from_cols_array_2d(&self.camera_uniform.view_proj),
            background_color: [0.0, 0.5, 0.2, 1.0], // Nice green background
            clear_background: self.clear_depth,
            draw_commands: self.draw_commands.iter().map(|cmd| {
                crate::vr::VrDrawCommand {
                    mesh_id: cmd.mesh_id,
                    transform: cmd.transform,
                }
            }).collect(),
        }
    }

    /// Get mesh data for VR rendering
    pub fn get_mesh_data(&self, mesh_id: u64) -> Option<&Mesh> {
        self.meshes.get(&mesh_id)
    }

    /// Get all mesh data for VR rendering
    pub fn get_all_meshes(&self) -> &HashMap<u64, Mesh> {
        &self.meshes
    }

    /// Get current draw commands
    pub fn get_draw_commands(&self) -> &Vec<DrawCommand> {
        &self.draw_commands
    }

    /// Get current camera view-projection matrix
    pub fn get_camera_view_proj(&self) -> glam::Mat4 {
        glam::Mat4::from_cols_array_2d(&self.camera_uniform.view_proj)
    }

    /// Switches to a different shader program
    pub fn switch_shader_program(&mut self, device: &wgpu::Device, surface_format: wgpu::TextureFormat, program_name: &str) {
        if !self.shader_manager.has_shader_program(program_name) {
            println!("WARNING: Shader program '{}' not found", program_name);
            return;
        }

        // Recreate the pipeline with the new shader
        self.pipeline = Self::create_pipeline_from_shader_manager(
            device,
            surface_format,
            &self.camera_bind_group_layout,
            &self.buffer_bind_group_layout,
            &self.shader_manager,
            program_name,
        );

        // Update current shader tracking
        self.current_shader_program = program_name.to_string();
    }

    /// Toggles face culling for debugging
    pub fn toggle_face_culling(&mut self, device: &wgpu::Device, surface_format: wgpu::TextureFormat) {
        // Recreate pipeline with no culling to see if spheres appear
        self.pipeline = Self::create_pipeline_with_culling(
            device,
            surface_format,
            &self.camera_bind_group_layout,
            &self.buffer_bind_group_layout,
            &self.shader_manager,
            &self.current_shader_program,
            None, // No culling
        );
    }

    /// Get mutable access to the shader manager
    pub fn shader_manager_mut(&mut self) -> &mut ShaderManager {
        &mut self.shader_manager
    }

    /// Get immutable access to the shader manager
    pub fn shader_manager(&self) -> &ShaderManager {
        &self.shader_manager
    }



    /// Checks for shader file changes and hot-reloads if needed
    pub fn check_shader_updates(&mut self, device: &wgpu::Device, surface_format: wgpu::TextureFormat) {
        let (updated_programs, failed_programs) = self.shader_manager.check_for_updates();

        if !updated_programs.is_empty() {
            // Check if the currently active shader was updated
            if updated_programs.contains(&self.current_shader_program) {
                // Recreate the pipeline with the current shader (maintaining the active shader)
                self.pipeline = Self::create_pipeline_from_shader_manager(
                    device,
                    surface_format,
                    &self.camera_bind_group_layout,
                    &self.buffer_bind_group_layout,
                    &self.shader_manager,
                    &self.current_shader_program, // Use the currently active shader
                );
            }
        }

        // Handle shader compilation failures
        if !failed_programs.is_empty() {
            // Check if the currently active shader failed
            if failed_programs.contains(&self.current_shader_program) {
                // Switch to error shader as fallback
                if self.shader_manager.has_shader_program("error") {
                    self.switch_shader_program(device, surface_format, "error");
                }
            }
        }
    }



    /// Create smart batches from a specific set of draw commands (used for culled objects)
    fn create_smart_batches_from_commands(&self, draw_commands: &[DrawCommand]) -> Vec<RenderBatch> {
        let mut batches: HashMap<u64, RenderBatch> = HashMap::new();

        // Group draw commands by mesh_id
        for draw_command in draw_commands {
            match batches.get_mut(&draw_command.mesh_id) {
                Some(batch) => {
                    // Add to existing batch
                    batch.add_transform(draw_command.transform);
                }
                None => {
                    // Create new batch
                    let batch = RenderBatch::new(draw_command.mesh_id, draw_command.transform);
                    batches.insert(draw_command.mesh_id, batch);
                }
            }
        }

        // Convert to vector and print batching decisions
        let batch_vec: Vec<RenderBatch> = batches.into_values().collect();

        // Debug output showing smart batching decisions
        let mut instanced_objects = 0;
        let mut dynamic_uniform_objects = 0;
        let mut instanced_batches = 0;
        let mut dynamic_uniform_batches = 0;

        for batch in &batch_vec {
            match &batch.batch_type {
                BatchType::Instanced { count } => {
                    instanced_objects += count;
                    instanced_batches += 1;
                    log_debug!("SMART BATCH: Mesh {} → INSTANCED ({} objects, 1 draw call)",
                             batch.mesh_id, count);
                }
                BatchType::DynamicUniform { count } => {
                    dynamic_uniform_objects += count;
                    dynamic_uniform_batches += 1;
                    log_debug!("SMART BATCH: Mesh {} → DYNAMIC UNIFORM ({} objects, {} draw calls)",
                             batch.mesh_id, count, count);
                }
            }
        }

        log_debug!("SMART BATCHING SUMMARY (CULLED):");
        log_debug!("  Instanced: {} objects in {} batches", instanced_objects, instanced_batches);
        log_debug!("  Dynamic Uniform: {} objects in {} batches", dynamic_uniform_objects, dynamic_uniform_batches);
        log_debug!("  Total draw calls: {} (vs {} without batching)",
                 instanced_batches + dynamic_uniform_objects,
                 draw_commands.len());

        batch_vec
    }

    /// Ensure we have an instance buffer for the given mesh with enough capacity
    /// This implements dynamic instance buffer resizing similar to dynamic uniform buffers
    fn ensure_instance_buffer(&mut self, device: &wgpu::Device, mesh_id: u64, required_instances: usize) {
        let needs_resize = match self.instance_buffers.get(&mesh_id) {
            Some(_) => {
                // Check if current buffer is too small
                required_instances > self.max_instances_per_mesh
            }
            None => true, // Buffer doesn't exist
        };

        if needs_resize {
            // Calculate new buffer size - grow by 2x when needed, minimum 10k instances
            let new_capacity = if required_instances > self.max_instances_per_mesh {
                // Grow to next power of 2 that fits the requirement
                let mut capacity = self.max_instances_per_mesh.max(10000);
                while capacity < required_instances {
                    capacity *= 2;
                }
                capacity
            } else {
                self.max_instances_per_mesh.max(10000)
            };

            let instance_buffer_size = std::mem::size_of::<InstanceData>() * new_capacity;

            let buffer = device.create_buffer(&wgpu::BufferDescriptor {
                label: Some(&format!("Instance Buffer for Mesh {} (Dynamic)", mesh_id)),
                size: instance_buffer_size as u64,
                usage: wgpu::BufferUsages::VERTEX | wgpu::BufferUsages::COPY_DST,
                mapped_at_creation: false,
            });

            // Update capacity if we grew the buffer
            if new_capacity > self.max_instances_per_mesh {
                self.max_instances_per_mesh = new_capacity;
            }

            self.instance_buffers.insert(mesh_id, buffer);
        }
    }

    /// Ensure we have enough buffers for the given number of objects
    /// This is the core of the dynamic buffer allocation system
    fn ensure_buffer_capacity(&mut self, device: &wgpu::Device, object_count: usize) {
        let buffers_needed = (object_count + self.objects_per_buffer - 1) / self.objects_per_buffer; // Ceiling division

        // Expand buffer pool if needed
        while self.model_buffer_pool.len() < buffers_needed {
            let buffer_size = self.model_uniform_size * self.objects_per_buffer as u64;

            // Create new buffer
            let buffer = device.create_buffer(&wgpu::BufferDescriptor {
                label: Some(&format!("Dynamic Model Buffer {}", self.model_buffer_pool.len())),
                size: buffer_size,
                usage: wgpu::BufferUsages::UNIFORM | wgpu::BufferUsages::COPY_DST,
                mapped_at_creation: false,
            });

            // Create corresponding bind group
            let bind_group = device.create_bind_group(&wgpu::BindGroupDescriptor {
                layout: &self.buffer_bind_group_layout,
                entries: &[wgpu::BindGroupEntry {
                    binding: 0,
                    resource: wgpu::BindingResource::Buffer(wgpu::BufferBinding {
                        buffer: &buffer,
                        offset: 0,
                        size: Some(wgpu::BufferSize::new(self.model_uniform_size).unwrap()),
                    }),
                }],
                label: Some(&format!("Model Bind Group {}", self.model_buffer_pool.len())),
            });

            self.model_buffer_pool.push(buffer);
            self.model_bind_group_pool.push(bind_group);
        }
    }







    /// SMART AUTO-BATCHING RENDER SYSTEM: Single render pass with automatic optimization
    /// Automatically chooses between instanced rendering (3+ identical objects) and
    /// dynamic uniform buffers (1-2 objects) for maximum performance
    pub fn render_consolidated(&mut self,
                              device: &wgpu::Device,
                              queue: &wgpu::Queue,
                              encoder: &mut wgpu::CommandEncoder,
                              color_view: &wgpu::TextureView,
                              background_clear: Option<wgpu::Color>) {

        // Handle empty scene - just clear buffers
        if self.draw_commands.is_empty() {
            let color_load_op = if let Some(clear_color) = background_clear {
                wgpu::LoadOp::Clear(clear_color)
            } else {
                wgpu::LoadOp::Load
            };

            let _render_pass = encoder.begin_render_pass(&wgpu::RenderPassDescriptor {
                label: Some("Empty Scene Clear Pass"),
                color_attachments: &[Some(wgpu::RenderPassColorAttachment {
                    view: color_view,
                    resolve_target: None,
                    ops: wgpu::Operations {
                        load: color_load_op,
                        store: wgpu::StoreOp::Store,
                    },
                })],
                depth_stencil_attachment: Some(wgpu::RenderPassDepthStencilAttachment {
                    view: &self.depth_view,
                    depth_ops: Some(wgpu::Operations {
                        load: if self.clear_depth { wgpu::LoadOp::Clear(1.0) } else { wgpu::LoadOp::Load },
                        store: wgpu::StoreOp::Store,
                    }),
                    stencil_ops: None,
                }),
                occlusion_query_set: None,
                timestamp_writes: None,
            });
            return;
        }

        // STEP 1: Smart auto-batching - group objects by mesh and auto-detect optimal rendering method
        let batches = self.create_smart_batches_from_commands(&self.draw_commands);

        // STEP 2: Prepare buffers for both instanced and dynamic uniform rendering
        let mut dynamic_uniform_objects = 0;
        for batch in &batches {
            match &batch.batch_type {
                BatchType::Instanced { count } => {
                    // Ensure instance buffer exists for this mesh with enough capacity
                    self.ensure_instance_buffer(device, batch.mesh_id, *count);
                }
                BatchType::DynamicUniform { count } => {
                    dynamic_uniform_objects += *count;
                }
            }
        }

        // Ensure dynamic uniform buffer capacity for non-instanced objects
        if dynamic_uniform_objects > 0 {
            self.ensure_buffer_capacity(device, dynamic_uniform_objects);
        }

        // STEP 2: Update camera buffer if needed
        if self.camera_dirty {
            queue.write_buffer(&self.camera_buffer, 0, bytemuck::cast_slice(&[self.camera_uniform]));
            self.camera_dirty = false;
        }

        // STEP 4: Prepare data for both rendering methods
        let mut model_uniforms = Vec::new();

        for batch in &batches {
            match &batch.batch_type {
                BatchType::Instanced { .. } => {
                    // Prepare instance data for instanced rendering
                    let instance_data: Vec<InstanceData> = batch.transforms
                        .iter()
                        .map(|&transform| InstanceData::new(transform))
                        .collect();

                    // Write instance data to instance buffer
                    if let Some(instance_buffer) = self.instance_buffers.get(&batch.mesh_id) {
                        let instance_bytes = bytemuck::cast_slice(&instance_data);
                        queue.write_buffer(instance_buffer, 0, instance_bytes);

                        log_debug!("INSTANCE DATA: Mesh {} - {} instances ({} bytes)",
                                 batch.mesh_id, instance_data.len(), instance_bytes.len());
                    }
                }
                BatchType::DynamicUniform { .. } => {
                    // Prepare model uniforms for dynamic uniform buffer rendering
                    for &transform in &batch.transforms {
                        let mut model_uniform = ModelUniform::new();
                        model_uniform.update_model(transform);
                        model_uniforms.push(model_uniform);
                    }
                }
            }
        }

        // Write dynamic uniform buffer data (batched for performance)
        if !model_uniforms.is_empty() {
            for (buffer_index, chunk) in model_uniforms.chunks(self.objects_per_buffer).enumerate() {
                if buffer_index < self.model_buffer_pool.len() {
                    let buffer = &self.model_buffer_pool[buffer_index];

                    // Prepare all uniform data in a single contiguous buffer
                    let uniform_size_bytes = self.model_uniform_size as usize;
                    let mut batch_data = Vec::with_capacity(chunk.len() * uniform_size_bytes);

                    for model_uniform in chunk.iter() {
                        let uniform_array = [*model_uniform];
                        let uniform_bytes = bytemuck::cast_slice(&uniform_array);

                        // Add the uniform data
                        batch_data.extend_from_slice(uniform_bytes);

                        // Add padding to reach the aligned size (256 bytes)
                        let padding_needed = uniform_size_bytes - uniform_bytes.len();
                        if padding_needed > 0 {
                            batch_data.resize(batch_data.len() + padding_needed, 0);
                        }
                    }

                    // Single batched write instead of multiple individual writes!
                    queue.write_buffer(buffer, 0, &batch_data);

                    log_debug!("DYNAMIC UNIFORM BATCH: Buffer {} - {} objects ({} bytes)",
                             buffer_index, chunk.len(), batch_data.len());
                }
            }
        }

        // STEP 5: Single render pass with smart auto-batching
        let color_load_op = if let Some(clear_color) = background_clear {
            wgpu::LoadOp::Clear(clear_color)
        } else {
            wgpu::LoadOp::Load
        };

        let mut render_pass = encoder.begin_render_pass(&wgpu::RenderPassDescriptor {
            label: Some("Smart Auto-Batching Render Pass"),
            color_attachments: &[Some(wgpu::RenderPassColorAttachment {
                view: color_view,
                resolve_target: None,
                ops: wgpu::Operations {
                    load: color_load_op,
                    store: wgpu::StoreOp::Store,
                },
            })],
            depth_stencil_attachment: Some(wgpu::RenderPassDepthStencilAttachment {
                view: &self.depth_view,
                depth_ops: Some(wgpu::Operations {
                    load: if self.clear_depth { wgpu::LoadOp::Clear(1.0) } else { wgpu::LoadOp::Load },
                    store: wgpu::StoreOp::Store,
                }),
                stencil_ops: None,
            }),
            occlusion_query_set: None,
            timestamp_writes: None,
        });

        // Set up pipeline and camera binding once
        render_pass.set_pipeline(&self.pipeline);
        render_pass.set_bind_group(0, &self.camera_bind_group, &[]);

        // STEP 6: Render batches using optimal method for each
        let mut dynamic_uniform_index = 0;

        for batch in &batches {
            if let Some(mesh) = self.meshes.get(&batch.mesh_id) {
                match &batch.batch_type {
                    BatchType::Instanced { count } => {
                        // INSTANCED RENDERING: 1 draw call for multiple objects
                        if let Some(instance_buffer) = self.instance_buffers.get(&batch.mesh_id) {
                            // For instanced rendering, we still need a dummy model uniform bind group
                            // The shader will use the instance data instead of the model uniform
                            if !self.model_bind_group_pool.is_empty() {
                                render_pass.set_bind_group(1, &self.model_bind_group_pool[0], &[0]);
                            }

                            // Bind mesh vertex buffer
                            render_pass.set_vertex_buffer(0, mesh.vertex_buffer().slice(..));
                            // Bind instance buffer
                            render_pass.set_vertex_buffer(1, instance_buffer.slice(..));
                            render_pass.set_index_buffer(mesh.index_buffer().slice(..), wgpu::IndexFormat::Uint32);

                            // Draw all instances in one call!
                            render_pass.draw_indexed(0..mesh.index_count(), 0, 0..(*count as u32));
                        }
                    }
                    BatchType::DynamicUniform { count } => {
                        // DYNAMIC UNIFORM RENDERING: Individual draw calls with different transforms
                        // Use dummy instance buffer since shader expects both vertex streams
                        for _i in 0..*count {
                            let buffer_index = dynamic_uniform_index / self.objects_per_buffer;
                            let local_index = dynamic_uniform_index % self.objects_per_buffer;
                            let dynamic_offset = (local_index as u64 * self.model_uniform_size) as u32;

                            if buffer_index < self.model_bind_group_pool.len() {
                                render_pass.set_bind_group(1, &self.model_bind_group_pool[buffer_index], &[dynamic_offset]);

                                // Bind mesh buffers and dummy instance buffer (with zero data)
                                render_pass.set_vertex_buffer(0, mesh.vertex_buffer().slice(..));
                                render_pass.set_vertex_buffer(1, self.dummy_instance_buffer.slice(..));
                                render_pass.set_index_buffer(mesh.index_buffer().slice(..), wgpu::IndexFormat::Uint32);
                                render_pass.draw_indexed(0..mesh.index_count(), 0, 0..1);
                            }

                            dynamic_uniform_index += 1;
                        }
                    }
                }
            }
        }

        // Render pass automatically ends when dropped - SINGLE RENDER PASS WITH SMART AUTO-BATCHING!
    }

    /// SMART AUTO-BATCHING RENDER SYSTEM with custom render targets
    /// Same as render_consolidated but allows custom color and depth targets
    /// Used by CPU Frame Ahead system for rendering to separate textures
    pub fn render_consolidated_to_targets(&mut self,
                                         device: &wgpu::Device,
                                         queue: &wgpu::Queue,
                                         encoder: &mut wgpu::CommandEncoder,
                                         color_view: &wgpu::TextureView,
                                         depth_view: &wgpu::TextureView,
                                         background_clear: Option<wgpu::Color>) {
        // Update camera uniforms if needed
        if self.camera_dirty {
            queue.write_buffer(&self.camera_buffer, 0, bytemuck::cast_slice(&[self.camera_uniform]));
            self.camera_dirty = false;
        }

        // Determine color load operation
        let color_load_op = if let Some(clear_color) = background_clear {
            wgpu::LoadOp::Clear(clear_color)
        } else {
            wgpu::LoadOp::Load
        };

        // Early exit if no objects to render (but still clear if requested)
        if self.draw_commands.is_empty() {
            let _render_pass = encoder.begin_render_pass(&wgpu::RenderPassDescriptor {
                label: Some("Empty Scene Clear Pass (Custom Targets)"),
                color_attachments: &[Some(wgpu::RenderPassColorAttachment {
                    view: color_view,
                    resolve_target: None,
                    ops: wgpu::Operations {
                        load: color_load_op,
                        store: wgpu::StoreOp::Store,
                    },
                })],
                depth_stencil_attachment: Some(wgpu::RenderPassDepthStencilAttachment {
                    view: depth_view, // Use custom depth view
                    depth_ops: Some(wgpu::Operations {
                        load: if self.clear_depth { wgpu::LoadOp::Clear(1.0) } else { wgpu::LoadOp::Load },
                        store: wgpu::StoreOp::Store,
                    }),
                    stencil_ops: None,
                }),
                occlusion_query_set: None,
                timestamp_writes: None,
            });
            return;
        }

        // STEP 1: Smart auto-batching - group objects by mesh and auto-detect optimal rendering method
        let batches = self.create_smart_batches_from_commands(&self.draw_commands);

        // STEP 2: Prepare buffers for both instanced and dynamic uniform rendering
        let mut dynamic_uniform_objects = 0;
        for batch in &batches {
            match &batch.batch_type {
                BatchType::Instanced { count } => {
                    // Ensure instance buffer exists for this mesh with enough capacity
                    self.ensure_instance_buffer(device, batch.mesh_id, *count);
                }
                BatchType::DynamicUniform { count } => {
                    dynamic_uniform_objects += *count;
                }
            }
        }

        // Ensure dynamic uniform buffer capacity for non-instanced objects
        if dynamic_uniform_objects > 0 {
            self.ensure_buffer_capacity(device, dynamic_uniform_objects);
        }

        // STEP 3: Prepare instance data for instanced rendering
        for batch in &batches {
            match &batch.batch_type {
                BatchType::Instanced { .. } => {
                    // Prepare instance data for this mesh
                    let instance_data: Vec<InstanceData> = batch.transforms.iter()
                        .map(|&transform| InstanceData::new(transform))
                        .collect();

                    // Write instance data to buffer
                    if let Some(instance_buffer) = self.instance_buffers.get(&batch.mesh_id) {
                        queue.write_buffer(instance_buffer, 0, bytemuck::cast_slice(&instance_data));
                    }
                }
                BatchType::DynamicUniform { .. } => {
                    // Dynamic uniform data will be prepared below
                }
            }
        }

        // STEP 4: Prepare model uniforms for dynamic uniform buffer rendering
        let mut model_uniforms = Vec::new();
        for batch in &batches {
            match &batch.batch_type {
                BatchType::Instanced { .. } => {
                    // Instanced rendering doesn't use model uniforms
                }
                BatchType::DynamicUniform { .. } => {
                    // Prepare model uniforms for dynamic uniform buffer rendering
                    for &transform in &batch.transforms {
                        let mut model_uniform = ModelUniform::new();
                        model_uniform.update_model(transform);
                        model_uniforms.push(model_uniform);
                    }
                }
            }
        }

        // Write dynamic uniform buffer data (batched for performance)
        if !model_uniforms.is_empty() {
            for (buffer_index, chunk) in model_uniforms.chunks(self.objects_per_buffer).enumerate() {
                if buffer_index < self.model_buffer_pool.len() {
                    let buffer = &self.model_buffer_pool[buffer_index];

                    // Prepare all uniform data in a single contiguous buffer
                    let uniform_size_bytes = self.model_uniform_size as usize;
                    let mut batch_data = Vec::with_capacity(chunk.len() * uniform_size_bytes);

                    for uniform in chunk {
                        let uniform_array = [*uniform];
                        let uniform_bytes = bytemuck::cast_slice(&uniform_array);
                        batch_data.extend_from_slice(uniform_bytes);

                        // Pad to alignment if needed
                        while batch_data.len() % uniform_size_bytes != 0 {
                            batch_data.push(0);
                        }
                    }

                    // Single write operation for the entire chunk
                    queue.write_buffer(buffer, 0, &batch_data);
                }
            }
        }

        // STEP 5: SINGLE RENDER PASS - Render all batches with smart auto-batching
        let mut render_pass = encoder.begin_render_pass(&wgpu::RenderPassDescriptor {
            label: Some("3D Render Pass (Custom Targets)"),
            color_attachments: &[Some(wgpu::RenderPassColorAttachment {
                view: color_view,
                resolve_target: None,
                ops: wgpu::Operations {
                    load: color_load_op,
                    store: wgpu::StoreOp::Store,
                },
            })],
            depth_stencil_attachment: Some(wgpu::RenderPassDepthStencilAttachment {
                view: depth_view, // Use custom depth view
                depth_ops: Some(wgpu::Operations {
                    load: if self.clear_depth { wgpu::LoadOp::Clear(1.0) } else { wgpu::LoadOp::Load },
                    store: wgpu::StoreOp::Store,
                }),
                stencil_ops: None,
            }),
            occlusion_query_set: None,
            timestamp_writes: None,
        });

        // Set pipeline and camera bind group (shared by all objects)
        render_pass.set_pipeline(&self.pipeline);
        render_pass.set_bind_group(0, &self.camera_bind_group, &[]);

        // STEP 6: Render all batches with automatic optimization
        let mut dynamic_uniform_index = 0;

        for batch in &batches {
            if let Some(mesh) = self.meshes.get(&batch.mesh_id) {
                match &batch.batch_type {
                    BatchType::Instanced { count } => {
                        // INSTANCED RENDERING: 1 draw call for multiple objects
                        if let Some(instance_buffer) = self.instance_buffers.get(&batch.mesh_id) {
                            // For instanced rendering, we still need a dummy model uniform bind group
                            // The shader will use the instance data instead of the model uniform
                            if !self.model_bind_group_pool.is_empty() {
                                render_pass.set_bind_group(1, &self.model_bind_group_pool[0], &[0]);
                            }

                            // Bind mesh vertex buffer
                            render_pass.set_vertex_buffer(0, mesh.vertex_buffer().slice(..));
                            // Bind instance buffer
                            render_pass.set_vertex_buffer(1, instance_buffer.slice(..));
                            render_pass.set_index_buffer(mesh.index_buffer().slice(..), wgpu::IndexFormat::Uint32);

                            // Draw all instances in one call!
                            render_pass.draw_indexed(0..mesh.index_count(), 0, 0..(*count as u32));
                        }
                    }
                    BatchType::DynamicUniform { count } => {
                        // DYNAMIC UNIFORM RENDERING: Individual draw calls with different transforms
                        // Use dummy instance buffer since shader expects both vertex streams
                        for _i in 0..*count {
                            let buffer_index = dynamic_uniform_index / self.objects_per_buffer;
                            let local_index = dynamic_uniform_index % self.objects_per_buffer;
                            let dynamic_offset = (local_index as u64 * self.model_uniform_size) as u32;

                            if buffer_index < self.model_bind_group_pool.len() {
                                render_pass.set_bind_group(1, &self.model_bind_group_pool[buffer_index], &[dynamic_offset]);

                                // Bind mesh buffers and dummy instance buffer (with zero data)
                                render_pass.set_vertex_buffer(0, mesh.vertex_buffer().slice(..));
                                render_pass.set_vertex_buffer(1, self.dummy_instance_buffer.slice(..));
                                render_pass.set_index_buffer(mesh.index_buffer().slice(..), wgpu::IndexFormat::Uint32);
                                render_pass.draw_indexed(0..mesh.index_count(), 0, 0..1);
                            }

                            dynamic_uniform_index += 1;
                        }
                    }
                }
            }
        }

        // Render pass automatically ends when dropped - SINGLE RENDER PASS WITH SMART AUTO-BATCHING!
    }

    /// Set VR camera matrices for multiview rendering
    pub fn set_vr_cameras(&mut self, left_eye: Mat4, right_eye: Mat4, desktop: Mat4) {
        let old_matrices = (
            self.vr_camera_uniform.left_eye_view_proj,
            self.vr_camera_uniform.right_eye_view_proj,
            self.vr_camera_uniform.desktop_view_proj,
        );

        self.vr_camera_uniform.update_matrices(left_eye, right_eye, desktop);

        let new_matrices = (
            self.vr_camera_uniform.left_eye_view_proj,
            self.vr_camera_uniform.right_eye_view_proj,
            self.vr_camera_uniform.desktop_view_proj,
        );

        if old_matrices != new_matrices {
            self.vr_camera_dirty = true;
        }
    }

    /// Render with VR multiview (3 views: left eye, right eye, desktop)
    pub fn render_multiview(
        &mut self,
        device: &wgpu::Device,
        queue: &wgpu::Queue,
        desktop_view_proj: Mat4,
        left_eye_view_proj: Mat4,
        right_eye_view_proj: Mat4,
        views: &[&wgpu::TextureView], // [desktop_view, left_eye_view, right_eye_view]
        background_clear: Option<wgpu::Color>,
    ) {
        // Always render to desktop view first
        if !views.is_empty() {
            let desktop_view = views[0];

            // Render to desktop using regular pipeline
            let mut encoder = device.create_command_encoder(&wgpu::CommandEncoderDescriptor {
                label: Some("Desktop Render Encoder"),
            });
            self.render_consolidated(device, queue, &mut encoder, desktop_view, background_clear);

            // Submit the command buffer
            queue.submit(std::iter::once(encoder.finish()));
        }

        // If VR views are available, render to them as well
        if views.len() >= 3 {
            let left_eye_view = views[1];
            let right_eye_view = views[2];

            // Render left eye view
            let mut left_encoder = device.create_command_encoder(&wgpu::CommandEncoderDescriptor {
                label: Some("VR Left Eye Render Encoder"),
            });
            self.render_to_vr_view(device, queue, &mut left_encoder, left_eye_view, left_eye_view_proj, background_clear);
            queue.submit(std::iter::once(left_encoder.finish()));

            // Render right eye view
            let mut right_encoder = device.create_command_encoder(&wgpu::CommandEncoderDescriptor {
                label: Some("VR Right Eye Render Encoder"),
            });
            self.render_to_vr_view(device, queue, &mut right_encoder, right_eye_view, right_eye_view_proj, background_clear);
            queue.submit(std::iter::once(right_encoder.finish()));

            log_info!("VR: Rendered desktop + stereo views ({} objects)", self.draw_commands.len());
            return;
        } else if !views.is_empty() {
            log_info!("VR: Rendered desktop view only ({} objects)", self.draw_commands.len());
            return;
        }

        // Check if multiview pipeline is available
        let has_multiview_pipeline = self.multiview_pipeline.is_some();
        if !has_multiview_pipeline {
            println!("WARNING: Multiview pipeline not available - VR rendering disabled");
            return;
        }

        // Update VR camera matrices
        self.set_vr_cameras(left_eye_view_proj, right_eye_view_proj, desktop_view_proj);

        // Update camera uniforms if dirty
        if self.camera_dirty {
            queue.write_buffer(&self.camera_buffer, 0, bytemuck::cast_slice(&[self.camera_uniform]));
            self.camera_dirty = false;
        }

        if self.vr_camera_dirty {
            queue.write_buffer(&self.vr_camera_buffer, 0, bytemuck::cast_slice(&[self.vr_camera_uniform]));
            self.vr_camera_dirty = false;
        }

        // Handle empty scene
        if self.draw_commands.is_empty() {
            return;
        }

        // Create smart batches for rendering
        let batches = self.create_smart_batches_from_commands(&self.draw_commands);

        // Prepare uniform data for dynamic uniform batches
        let mut dynamic_uniforms = Vec::new();
        for batch in &batches {
            if let BatchType::DynamicUniform { .. } = &batch.batch_type {
                for transform in &batch.transforms {
                    dynamic_uniforms.push(ModelUniform {
                        model: transform.to_cols_array_2d(),
                    });
                }
            }
        }

        // Ensure buffer capacity and upload uniform data
        if !dynamic_uniforms.is_empty() {
            self.ensure_buffer_capacity(device, dynamic_uniforms.len());

            // Upload uniform data in chunks
            for (chunk_index, chunk) in dynamic_uniforms.chunks(self.objects_per_buffer).enumerate() {
                if chunk_index < self.model_buffer_pool.len() {
                    let buffer = &self.model_buffer_pool[chunk_index];
                    let uniform_size_bytes = self.model_uniform_size as usize;
                    let mut batch_data = Vec::with_capacity(chunk.len() * uniform_size_bytes);

                    for uniform in chunk {
                        let uniform_array = [*uniform];
                        let uniform_bytes = bytemuck::cast_slice(&uniform_array);
                        batch_data.extend_from_slice(uniform_bytes);

                        while batch_data.len() % uniform_size_bytes != 0 {
                            batch_data.push(0);
                        }
                    }

                    queue.write_buffer(buffer, 0, &batch_data);
                }
            }
        }

        // Prepare instance data for instanced batches
        for batch in &batches {
            if let BatchType::Instanced { .. } = &batch.batch_type {
                self.ensure_instance_buffer(device, batch.mesh_id, batch.transforms.len());

                if let Some(instance_buffer) = self.instance_buffers.get(&batch.mesh_id) {
                    let instance_data: Vec<InstanceData> = batch.transforms.iter()
                        .map(|transform| InstanceData {
                            transform: transform.to_cols_array_2d(),
                        })
                        .collect();

                    queue.write_buffer(instance_buffer, 0, bytemuck::cast_slice(&instance_data));
                }
            }
        }

        // Create multiview render pass
        let mut encoder = device.create_command_encoder(&wgpu::CommandEncoderDescriptor {
            label: Some("VR Multiview Render Encoder"),
        });

        let color_load_op = if let Some(clear_color) = background_clear {
            wgpu::LoadOp::Clear(clear_color)
        } else {
            wgpu::LoadOp::Load
        };

        // Create multiview depth texture for all 3 views
        let multiview_depth_texture = device.create_texture(&wgpu::TextureDescriptor {
            label: Some("Multiview Depth Texture"),
            size: wgpu::Extent3d {
                width: self.width,
                height: self.height,
                depth_or_array_layers: 3, // 3 views
            },
            mip_level_count: 1,
            sample_count: 1,
            dimension: wgpu::TextureDimension::D2,
            format: wgpu::TextureFormat::Depth32Float,
            usage: wgpu::TextureUsages::RENDER_ATTACHMENT,
            view_formats: &[],
        });

        let multiview_depth_view = multiview_depth_texture.create_view(&wgpu::TextureViewDescriptor {
            label: Some("Multiview Depth View"),
            format: Some(wgpu::TextureFormat::Depth32Float),
            dimension: Some(wgpu::TextureViewDimension::D2Array),
            aspect: wgpu::TextureAspect::All,
            base_mip_level: 0,
            mip_level_count: Some(1),
            base_array_layer: 0,
            array_layer_count: Some(3),
        });

        // Create multiview color texture array
        let multiview_color_texture = device.create_texture(&wgpu::TextureDescriptor {
            label: Some("Multiview Color Texture"),
            size: wgpu::Extent3d {
                width: self.width,
                height: self.height,
                depth_or_array_layers: 3, // 3 views
            },
            mip_level_count: 1,
            sample_count: 1,
            dimension: wgpu::TextureDimension::D2,
            format: wgpu::TextureFormat::Bgra8UnormSrgb, // Match surface format
            usage: wgpu::TextureUsages::RENDER_ATTACHMENT | wgpu::TextureUsages::COPY_SRC,
            view_formats: &[],
        });

        let multiview_color_view = multiview_color_texture.create_view(&wgpu::TextureViewDescriptor {
            label: Some("Multiview Color View"),
            format: Some(wgpu::TextureFormat::Bgra8UnormSrgb),
            dimension: Some(wgpu::TextureViewDimension::D2Array),
            aspect: wgpu::TextureAspect::All,
            base_mip_level: 0,
            mip_level_count: Some(1),
            base_array_layer: 0,
            array_layer_count: Some(3),
        });

        // Multiview render pass
        {
            let mut render_pass = encoder.begin_render_pass(&wgpu::RenderPassDescriptor {
                label: Some("VR Multiview Render Pass"),
                color_attachments: &[Some(wgpu::RenderPassColorAttachment {
                    view: &multiview_color_view,
                    resolve_target: None,
                    ops: wgpu::Operations {
                        load: color_load_op,
                        store: wgpu::StoreOp::Store,
                    },
                })],
                depth_stencil_attachment: Some(wgpu::RenderPassDepthStencilAttachment {
                    view: &multiview_depth_view,
                    depth_ops: Some(wgpu::Operations {
                        load: if self.clear_depth { wgpu::LoadOp::Clear(1.0) } else { wgpu::LoadOp::Load },
                        store: wgpu::StoreOp::Store,
                    }),
                    stencil_ops: None,
                }),
                occlusion_query_set: None,
                timestamp_writes: None,
            });

            // Set multiview pipeline and VR camera bind group
            render_pass.set_pipeline(self.multiview_pipeline.as_ref().unwrap());
            render_pass.set_bind_group(0, &self.vr_camera_bind_group, &[]);

            // Render all batches
            let mut dynamic_uniform_index = 0;

            for batch in &batches {
                if let Some(mesh) = self.meshes.get(&batch.mesh_id) {
                    match &batch.batch_type {
                        BatchType::Instanced { count } => {
                            if let Some(instance_buffer) = self.instance_buffers.get(&batch.mesh_id) {
                                if !self.model_bind_group_pool.is_empty() {
                                    render_pass.set_bind_group(1, &self.model_bind_group_pool[0], &[0]);
                                }

                                render_pass.set_vertex_buffer(0, mesh.vertex_buffer().slice(..));
                                render_pass.set_vertex_buffer(1, instance_buffer.slice(..));
                                render_pass.set_index_buffer(mesh.index_buffer().slice(..), wgpu::IndexFormat::Uint32);
                                render_pass.draw_indexed(0..mesh.index_count(), 0, 0..(*count as u32));
                            }
                        }
                        BatchType::DynamicUniform { count } => {
                            for _i in 0..*count {
                                let buffer_index = dynamic_uniform_index / self.objects_per_buffer;
                                let local_index = dynamic_uniform_index % self.objects_per_buffer;
                                let dynamic_offset = (local_index as u64 * self.model_uniform_size) as u32;

                                if buffer_index < self.model_bind_group_pool.len() {
                                    render_pass.set_bind_group(1, &self.model_bind_group_pool[buffer_index], &[dynamic_offset]);

                                    render_pass.set_vertex_buffer(0, mesh.vertex_buffer().slice(..));
                                    render_pass.set_vertex_buffer(1, self.dummy_instance_buffer.slice(..));
                                    render_pass.set_index_buffer(mesh.index_buffer().slice(..), wgpu::IndexFormat::Uint32);
                                    render_pass.draw_indexed(0..mesh.index_count(), 0, 0..1);
                                }

                                dynamic_uniform_index += 1;
                            }
                        }
                    }
                }
            }
        }

        // Note: The multiview rendering renders to all 3 views simultaneously
        // For desktop display, we need to copy the desktop view (layer 2) to the surface
        // For VR display, we need to copy layers 0 and 1 to VR swapchain
        // This is handled by the VR system and desktop rendering separately

        // TODO: Copy VR views (layers 0 and 1) to VR swapchain textures
        // This would require proper VR texture integration

        queue.submit(std::iter::once(encoder.finish()));
    }

    /// Render to a VR view with specific camera matrix
    fn render_to_vr_view(
        &mut self,
        device: &wgpu::Device,
        queue: &wgpu::Queue,
        encoder: &mut wgpu::CommandEncoder,
        view: &wgpu::TextureView,
        view_proj: Mat4,
        background_clear: Option<wgpu::Color>,
    ) {
        // Update camera uniform for this view
        self.camera_uniform.view_proj = view_proj.to_cols_array_2d();
        queue.write_buffer(&self.camera_buffer, 0, bytemuck::cast_slice(&[self.camera_uniform]));

        // Render using the consolidated pipeline
        self.render_consolidated(device, queue, encoder, view, background_clear);
    }
}
