use std::collections::HashMap;
use std::path::Path;
use anyhow::Result;

pub trait Resource {
    fn load_from_file(path: &Path) -> Result<Self>
    where
        Self: Sized;
}

pub struct ResourceManager<T> {
    resources: HashMap<String, T>,
}

impl<T> ResourceManager<T>
where
    T: Resource + Clone,
{
    pub fn new() -> Self {
        Self {
            resources: HashMap::new(),
        }
    }

    pub fn load(&mut self, name: &str, path: &Path) -> Result<&T> {
        if !self.resources.contains_key(name) {
            let resource = T::load_from_file(path)?;
            self.resources.insert(name.to_string(), resource);
        }
        Ok(self.resources.get(name).unwrap())
    }

    pub fn get(&self, name: &str) -> Option<&T> {
        self.resources.get(name)
    }

    pub fn insert(&mut self, name: String, resource: T) {
        self.resources.insert(name, resource);
    }

    pub fn remove(&mut self, name: &str) -> Option<T> {
        self.resources.remove(name)
    }

    pub fn clear(&mut self) {
        self.resources.clear();
    }

    pub fn contains(&self, name: &str) -> bool {
        self.resources.contains_key(name)
    }

    pub fn len(&self) -> usize {
        self.resources.len()
    }

    pub fn is_empty(&self) -> bool {
        self.resources.is_empty()
    }
}

impl<T> Default for ResourceManager<T>
where
    T: Resource + Clone,
{
    fn default() -> Self {
        Self::new()
    }
}

// Example implementation for a simple text resource
#[derive(Clone)]
pub struct TextResource {
    pub content: String,
}

impl Resource for TextResource {
    fn load_from_file(path: &Path) -> Result<Self> {
        let content = std::fs::read_to_string(path)?;
        Ok(Self { content })
    }
}
