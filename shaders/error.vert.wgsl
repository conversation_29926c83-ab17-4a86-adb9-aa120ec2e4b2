// Error vertex shader - used with error fragment shader when compilation fails
// This shader creates a simple vertex transformation for error visualization

struct CameraUniform {
    view_proj: mat4x4<f32>,
}

struct ModelUniform {
    model: mat4x4<f32>,
}

@group(0) @binding(0)
var<uniform> camera: CameraUniform;

@group(1) @binding(0)
var<uniform> model: ModelUniform;

struct VertexInput {
    @location(0) position: vec3<f32>,
    @location(1) normal: vec3<f32>,
    @location(2) tex_coords: vec2<f32>,
}

struct InstanceInput {
    @location(3) instance_matrix_0: vec4<f32>,
    @location(4) instance_matrix_1: vec4<f32>,
    @location(5) instance_matrix_2: vec4<f32>,
    @location(6) instance_matrix_3: vec4<f32>,
}

struct VertexOutput {
    @builtin(position) clip_position: vec4<f32>,
    @location(0) world_normal: vec3<f32>,
    @location(1) tex_coords: vec2<f32>,
}

@vertex
fn vs_main(input: VertexInput, instance: InstanceInput) -> VertexOutput {
    var out: VertexOutput;

    // Reconstruct instance matrix from the 4 vec4s
    let instance_matrix = mat4x4<f32>(
        instance.instance_matrix_0,
        instance.instance_matrix_1,
        instance.instance_matrix_2,
        instance.instance_matrix_3,
    );

    // Use instance matrix if available, otherwise use model uniform
    let is_instanced = length(instance.instance_matrix_0) > 0.1;

    var transform_matrix: mat4x4<f32>;
    if (is_instanced) {
        transform_matrix = instance_matrix;
    } else {
        transform_matrix = model.model;
    }

    // Transform vertex position
    let world_position = transform_matrix * vec4<f32>(input.position, 1.0);
    out.clip_position = camera.view_proj * world_position;

    // Transform normal to world space
    out.world_normal = (transform_matrix * vec4<f32>(input.normal, 0.0)).xyz;

    // Pass through texture coordinates
    out.tex_coords = input.tex_coords;

    return out;
}
