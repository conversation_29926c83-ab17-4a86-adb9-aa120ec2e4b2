// Test compute shader - demonstrates basic GPU computation
// This shader performs simple mathematical operations on buffer data

// Input/Output buffer - contains numbers to process
@group(0) @binding(0)
var<storage, read_write> data: array<f32>;

// Uniform buffer for parameters
struct ComputeParams {
    num_elements: u32,
    multiplier: f32,
    offset: f32,
    _padding: f32, // Align to 16 bytes
}

@group(0) @binding(1)
var<uniform> params: ComputeParams;

// Workgroup size - processes 64 elements per workgroup
@compute @workgroup_size(32, 1, 1)
fn main(@builtin(global_invocation_id) global_id: vec3<u32>) {
    let index = global_id.x;

    // Bounds check
    if (index >= params.num_elements) {
        return;
    }

    // Read current value
    let current_value = data[index];

    // 🚀 COMPLETELY NEW TEST: Make results EXTREMELY obvious!
    // Set everything to 9999.0 to prove the shader changed!
    data[index] = 1.0;

    // Add index to make each value unique
    data[index] = data[index] + f32(index);

    // now make the values increase exponentially
    data[index] = data[index] * data[index] * data[index];

}
