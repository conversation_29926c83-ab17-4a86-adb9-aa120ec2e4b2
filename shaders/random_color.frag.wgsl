struct VertexOutput {
    @location(0) world_normal: vec3<f32>,
    @location(1) tex_coords: vec2<f32>,
    // we need to pass the clip‐space position through so we can reconstruct screen coords
    @builtin(position) clip_position: vec4<f32>,
}

fn rand(seed: f32) -> f32 {
    // classic hash: fract(sin(x)*43758.5453)
    return fract(sin(seed) * 43758.5453);
}

@fragment
fn fs_main(input: VertexOutput) -> @location(0) vec4<f32> {
    // get normalized screen coords (0..1)
    let ndc = input.clip_position.xy / input.clip_position.w;        // -1..1
    let uv  = ndc * 0.5 + vec2<f32>(0.5, 0.5);                         //  0..1

    // derive three different seeds from uv
    let seed1 = uv.x * 12.9898 + uv.y * 78.233;
    let seed2 = uv.x * 93.9898 + uv.y * 67.345;
    let seed3 = uv.x * 45.332 +  uv.y * 01.879;

    // 🔥 TESTING FACE CULLING DEBUG SYSTEM! 🔥
    let r = rand(seed1) * 0.03; // Normal red
    let g = rand(seed2) * 0.03; // SUPER BRIGHT GREEN!
    let b = rand(seed3) * 0.03; // Very dim blue

    return vec4<f32>(r, g, b, 1.0);
}
