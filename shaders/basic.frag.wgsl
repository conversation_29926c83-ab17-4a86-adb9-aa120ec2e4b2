// Basic 3D fragment shader with simple lighting
struct VertexOutput {
    @location(0) world_normal: vec3<f32>,
    @location(1) tex_coords: vec2<f32>,
}

@fragment
fn fs_main(input: VertexOutput) -> @location(0) vec4<f32> {
    // Simple directional lighting
    let light_dir = normalize(vec3<f32>(1.0, 1.0, 1.0));
    let normal = normalize(input.world_normal);

    // Ambient + diffuse lighting
    let ambient = 0.3;
    let diffuse = max(dot(normal, light_dir), 0.0) * 0.7;
    let lighting = ambient + diffuse;

    // Base color (can be modified to use textures later)
    let base_color = vec3<f32>(0.8, 0.6, 0.4); // Nice orange-brown color

    return vec4<f32>(base_color * lighting, 1.0);
}
