// Textured fragment shader - samples texture and multiplies with vertex color
struct FragmentInput {
    @location(0) color: vec3<f32>,
    @location(1) uv: vec2<f32>,
}

@group(2) @binding(0)
var texture_sampler: sampler;

@group(2) @binding(1)
var texture_diffuse: texture_2d<f32>;

@fragment
fn fs_main(input: FragmentInput) -> @location(0) vec4<f32> {
    let texture_color = textureSample(texture_diffuse, texture_sampler, input.uv);
    let final_color = texture_color.rgb * input.color;
    return vec4<f32>(final_color, texture_color.a);
}
