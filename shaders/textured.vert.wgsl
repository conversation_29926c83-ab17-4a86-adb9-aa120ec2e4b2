// Textured vertex shader - transforms vertices and passes through UV coordinates
struct CameraUniform {
    view_proj: mat4x4<f32>,
}

struct ModelUniform {
    transform: mat4x4<f32>,
}

struct VertexInput {
    @location(0) position: vec3<f32>,
    @location(1) color: vec3<f32>,
    @location(2) uv: vec2<f32>,
}

struct VertexOutput {
    @builtin(position) clip_position: vec4<f32>,
    @location(0) color: vec3<f32>,
    @location(1) uv: vec2<f32>,
}

@group(0) @binding(0)
var<uniform> camera: CameraUniform;

@group(1) @binding(0)
var<uniform> model: ModelUniform;

@vertex
fn vs_main(input: VertexInput) -> VertexOutput {
    var out: VertexOutput;
    
    // Transform vertex position
    let world_position = model.transform * vec4<f32>(input.position, 1.0);
    out.clip_position = camera.view_proj * world_position;
    
    // Pass through color and UV coordinates
    out.color = input.color;
    out.uv = input.uv;
    
    return out;
}
