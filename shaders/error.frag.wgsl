// Error shader - displayed when shader compilation fails
// This shader creates a distinctive red/black checkerboard pattern to indicate errors

struct VertexOutput {
    @builtin(position) clip_position: vec4<f32>,
    @location(0) world_normal: vec3<f32>, // Match vertex shader output exactly
    @location(1) tex_coords: vec2<f32>,   // Match vertex shader output exactly
}

@fragment
fn fs_main(input: VertexOutput) -> @location(0) vec4<f32> {
    // Create a checkerboard pattern to indicate shader error
    let scale = 8.0; // Size of checkerboard squares
    let x = floor(input.tex_coords.x * scale);
    let y = floor(input.tex_coords.y * scale);

    // Checkerboard logic
    let checker = (x + y) % 2.0;

    if checker < 0.5 {
        // Red squares - ERROR COLOR
        return vec4<f32>(1.0, 0.0, 0.0, 1.0);
    } else {
        // Black squares
        return vec4<f32>(0.1, 0.0, 0.0, 1.0);
    }
}
