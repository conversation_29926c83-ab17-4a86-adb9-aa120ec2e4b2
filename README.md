# 3D Game Engine

A modular 3D game engine built with Rust and wgpu, designed with clean separation between the engine library and game applications.

## Project Structure

```
├── engine/          # Engine library crate
│   ├── src/
│   │   ├── core/    # Core engine systems
│   │   ├── graphics/ # Rendering and graphics
│   │   ├── input/   # Input handling
│   │   ├── math/    # Mathematical utilities
│   │   ├── resources/ # Resource management
│   │   ├── time/    # Time management
│   │   └── lib.rs   # Engine library entry point
│   └── Cargo.toml
├── game/            # Game application
│   ├── src/
│   │   ├── main.rs  # Game entry point
│   │   └── game_state.rs # Game-specific logic
│   └── Cargo.toml
├── cube example/    # Example project (preserved)
└── Cargo.toml       # Workspace configuration
```

## Features

### Engine Library (`game_engine`)
- **Core Systems**: Engine lifecycle and immediate mode rendering
- **Graphics**: wgpu-based renderer, camera system, mesh and material management
- **Input**: Keyboard and mouse input handling
- **Math**: Transform system, common mathematical utilities
- **Resources**: Generic resource management system
- **Time**: Delta time and FPS tracking

### Game Application
- Uses the engine as a dependency
- Implements game-specific logic
- Example camera controls and input handling
- Modular design for easy extension

## Building and Running

### Prerequisites
- Rust 1.70+ (2021 edition)
- A graphics driver that supports Vulkan, Metal, or DirectX 12

### Build the entire workspace
```bash
cargo build
```

### Run the game
```bash
cargo run --bin game
```

### Build just the engine library
```bash
cargo build -p game_engine
```

### Run tests
```bash
cargo test
```

## Usage

### Using the Engine in Your Game

Add the engine as a dependency in your game's `Cargo.toml`:

```toml
[dependencies]
game_engine = { path = "../engine" }
```

Basic usage example:

```rust
use game_engine::{Engine, init};
use winit::event_loop::EventLoop;

fn main() -> anyhow::Result<()> {
    init()?;

    let event_loop = EventLoop::new();
    let engine = pollster::block_on(Engine::new(&event_loop))?;

    engine.run(event_loop);
    Ok(())
}
```

### Key Controls (in the example game)
- `W/A/S/D`: Move camera
- `Right Mouse + Move`: Look around
- `Escape`: Exit (example)
- `Space`: Action (example)

## Architecture

The engine follows a modular architecture with clear separation of concerns:

1. **Engine Library**: Provides core functionality that can be reused across different games
2. **Game Application**: Implements game-specific logic using the engine
3. **Workspace**: Manages both crates together while maintaining independence

This design allows you to:
- Modify the engine without affecting game logic
- Create multiple games using the same engine
- Easily test and iterate on engine features
- Maintain clean dependencies

## Dependencies

- **wgpu**: Modern graphics API abstraction
- **winit**: Cross-platform windowing
- **glam**: Fast linear algebra library
- **bytemuck**: Safe transmutation between types
- **anyhow**: Error handling
- **log/env_logger**: Logging

## License

MIT OR Apache-2.0
