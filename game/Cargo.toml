[package]
name = "game"
version = "0.1.0"
edition = "2021"
description = "A 3D game using the game engine"

[[bin]]
name = "game"
path = "src/main.rs"

[dependencies]
# Local engine dependency
game_engine = { path = "../engine" }

# Additional game-specific dependencies
anyhow = { workspace = true }
log = { workspace = true }
env_logger = { workspace = true }
pollster = { workspace = true }

# Re-export engine math for convenience
glam = { workspace = true }
winit = { workspace = true }
bytemuck = { workspace = true }
rand = "0.8"

[features]
default = ["vr"]
debug = ["game_engine/debug"]
vr = ["game_engine/vr"]
