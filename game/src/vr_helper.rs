//! VR helper functions for the game
//! 
//! This module contains VR-specific rendering logic that is only compiled
//! when the VR feature is enabled.

use game_engine::{Engine, SimpleVrScene, SimpleVrObject, render_vr_scene};
use glam::{Mat4, Vec3};

/// Render VR scene with all the game objects
pub fn render_vr_scene_for_game(
    engine: &mut Engine,
    cube_mesh_id: u64,
    sphere_mesh_id: u64,
    rotation1: Mat4,
    transform2: Mat4,
    transform3: Mat4,
    random_spheres: &[(Vec3, f32)],
    time: f32,
    view_proj: Mat4,
) {
    if !engine.vr_renderer.is_enabled() {
        return;
    }

    // Create simple VR scene
    let mut vr_scene = SimpleVrScene {
        background_color: [0.0, 0.5, 0.2, 1.0], // Nice green background
        clear_background: true,
        objects: Vec::new(),
    };

    // Add main objects
    vr_scene.objects.push(SimpleVrObject {
        mesh_id: cube_mesh_id,
        transform: rotation1,
    });

    vr_scene.objects.push(SimpleVrObject {
        mesh_id: cube_mesh_id,
        transform: transform2,
    });

    vr_scene.objects.push(SimpleVrObject {
        mesh_id: sphere_mesh_id,
        transform: transform3,
    });

    // Add grid spheres
    let grid_size = 10;
    let spacing = 2.5;
    let grid_offset = Vec3::new(0.0, 0.0, -15.0);

    for row in 0..grid_size {
        for col in 0..grid_size {
            let x = (col as f32 - (grid_size as f32 - 1.0) / 2.0) * spacing;
            let y = (row as f32 - (grid_size as f32 - 1.0) / 2.0) * spacing;
            let position = grid_offset + Vec3::new(x, y, 0.0);

            let time_offset = (row * grid_size + col) as f32 * 0.2;
            let rotation_speed = 0.5 + (row + col) as f32 * 0.1;

            let translation = Mat4::from_translation(position);
            let scale = Mat4::from_scale(Vec3::splat(0.8));
            let rotation = Mat4::from_rotation_y((time + time_offset) * rotation_speed)
                         * Mat4::from_rotation_x((time + time_offset) * rotation_speed * 0.7);

            let transform = translation * scale * rotation;
            vr_scene.objects.push(SimpleVrObject {
                mesh_id: sphere_mesh_id,
                transform,
            });
        }
    }

    // Add random spheres
    for (index, (position, rotation_speed)) in random_spheres.iter().enumerate() {
        let time_offset = index as f32 * 0.3;
        let translation = Mat4::from_translation(*position);
        let scale = Mat4::from_scale(Vec3::splat(1.2));
        let rotation = Mat4::from_rotation_y((time + time_offset) * rotation_speed)
                     * Mat4::from_rotation_x((time + time_offset) * rotation_speed * 0.8);

        let transform = translation * scale * rotation;
        vr_scene.objects.push(SimpleVrObject {
            mesh_id: sphere_mesh_id,
            transform,
        });
    }

    // Convert to VR scene data and pass to VR renderer
    let vr_scene_data = render_vr_scene(vr_scene, view_proj);
    engine.vr_renderer.set_scene_data(vr_scene_data);
}
