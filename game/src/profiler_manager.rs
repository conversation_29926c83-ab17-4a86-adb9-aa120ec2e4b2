use game_engine::Engine;
use std::time::Instant;

/// Manages profiler-related state and functionality for the game
pub struct ProfilerManager {
    /// Timing for debug output printing
    last_debug_print: Instant,
    debug_print_interval: std::time::Duration,
    
    /// Window title and FPS tracking
    base_window_title: String,
    last_fps_update: Instant,
}

impl ProfilerManager {
    /// Create a new ProfilerManager with default settings
    pub fn new(base_window_title: String) -> Self {
        Self {
            last_debug_print: Instant::now(),
            debug_print_interval: std::time::Duration::from_secs(5),
            base_window_title,
            last_fps_update: Instant::now(),
        }
    }

    /// Begin profiling for the game update section
    pub fn begin_game_update_profiling(&self, engine: &mut Engine) {
        let profiler = engine.profiler();
        profiler.begin("game_update");
        profiler.begin("game_logic");
    }

    /// End profiling for the game update section
    pub fn end_game_update_profiling(&self, engine: &mut Engine) {
        let profiler = engine.profiler();
        profiler.end("game_logic");
        profiler.end("game_update");
    }

    /// Begin profiling for the 3D rendering section
    pub fn begin_3d_rendering_profiling(&self, engine: &mut Engine) {
        engine.profiler().begin("3d_rendering");
    }

    /// End profiling for the 3D rendering section
    pub fn end_3d_rendering_profiling(&self, engine: &mut Engine) {
        engine.profiler().end("3d_rendering");
    }

    /// Handle debug output printing at regular intervals
    pub fn handle_debug_output(&mut self, engine: &Engine) {
        let now = Instant::now();
        if now.duration_since(self.last_debug_print) >= self.debug_print_interval {
            let debug_text = engine.debug_text();
            if !debug_text.is_empty() {
                print!("{}", debug_text);
            }
            self.last_debug_print = now;
        }
    }

    /// Handle window title FPS updates
    pub fn handle_fps_window_title_update(&mut self, engine: &mut Engine) {
        let now = Instant::now();
        if now.duration_since(self.last_fps_update) >= std::time::Duration::from_millis(100) {
            let current_fps = engine.profiler.fps(); // Use same FPS as profiler for consistency

            // Add current FPS to time manager's rolling average
            engine.time_manager.add_fps_to_history(current_fps);
            let avg_fps = engine.time_manager.average_fps();

            let title = format!("{} - FPS: {:.1} (avg: {:.1})", self.base_window_title, current_fps, avg_fps);
            engine.window.set_title(&title);
            self.last_fps_update = now;
        }
    }

    /// Update all profiler-related functionality
    /// This is a convenience method that calls all the individual update methods
    pub fn update(&mut self, engine: &mut Engine) {
        self.handle_debug_output(engine);
        self.handle_fps_window_title_update(engine);
    }

    /// Get the base window title
    pub fn base_window_title(&self) -> &str {
        &self.base_window_title
    }

    /// Set a new base window title
    pub fn set_base_window_title(&mut self, title: String) {
        self.base_window_title = title;
    }

    /// Get the debug print interval
    pub fn debug_print_interval(&self) -> std::time::Duration {
        self.debug_print_interval
    }

    /// Set a new debug print interval
    pub fn set_debug_print_interval(&mut self, interval: std::time::Duration) {
        self.debug_print_interval = interval;
    }
}
