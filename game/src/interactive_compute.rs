use game_engine::{Engine, bytemuck};
use std::time::Instant;

/// Define the uniform parameters structure for compute shaders
#[repr(C)]
#[derive(Co<PERSON>, <PERSON>lone, bytemuck::Pod, bytemuck::Zeroable)]
pub struct ComputeParams {
    pub num_elements: u32,
    pub multiplier: f32,
    pub offset: f32,
    pub _padding: f32,
}

/// Creates an interactive compute job for testing GPU compute functionality
pub fn create_interactive_compute_job(engine: &mut Engine) -> Result<(), String> {
    // Create storage data (same as test_compute)
    let storage_data: Vec<f32> = (0..1024).map(|i| i as f32).collect();

    // Set up uniform parameters (same as test_compute)
    let params = ComputeParams {
        num_elements: storage_data.len() as u32,
        multiplier: 2.0,
        offset: 1.0,
        _padding: 0.0,
    };

    let params_array = [params];
    let uniform_data = bytemuck::cast_slice(&params_array);

    // Create the compute job (using same job name as test_compute for consistency)
    engine.create_compute_job(
        "interactive_job",              // Interactive job name (different from demo_test_job)
        "test_compute",                 // Same shader name
        "test_compute.comp.wgsl",      // Same shader file
        &storage_data,                  // Same storage data
        uniform_data,                   // Same uniform data
        (64, 1, 1),                    // Same workgroup size as test_compute
    )
}

/// Static variables for tracking timing of compute operations
pub struct ComputeTimingState {
    pub last_compute_init_time: Option<Instant>,
    pub last_compute_run_time: Option<Instant>,
}

impl ComputeTimingState {
    pub fn new() -> Self {
        Self {
            last_compute_init_time: None,
            last_compute_run_time: None,
        }
    }
}

/// Handles the initialization of interactive compute job when 'I' key is pressed
pub fn handle_init_compute_action(engine: &mut Engine, timing_state: &mut ComputeTimingState) {
    let now = Instant::now();
    let should_init = if let Some(last_time) = timing_state.last_compute_init_time {
        now.duration_since(last_time) > std::time::Duration::from_millis(1000)
    } else {
        true
    };

    if should_init {
        println!("🎮 [I] Creating interactive compute job...");
        match create_interactive_compute_job(engine) {
            Ok(_) => {
                println!("✅ [I] Interactive compute job 'interactive_job' created successfully!");
                println!("💡 [I] Press 'R' to execute the job and see results");
            }
            Err(e) => {
                println!("❌ [I] Failed to create interactive compute job: {}", e);
            }
        }

        timing_state.last_compute_init_time = Some(now);
    }
}

/// Handles the execution of interactive compute job when 'R' key is pressed
pub fn handle_run_compute_action(engine: &mut Engine, timing_state: &mut ComputeTimingState) {
    let now = Instant::now();
    let should_run = if let Some(last_time) = timing_state.last_compute_run_time {
        now.duration_since(last_time) > std::time::Duration::from_millis(500)
    } else {
        true
    };

    if should_run {
        println!("🎮 [R] Executing interactive compute job...");

        // Execute the compute job
        match engine.execute_compute_job("interactive_job") {
            Ok(_) => {
                println!("✅ [R] Compute job executed successfully!");

                // Read back the results from GPU
                println!("📥 [R] Reading computed results from GPU...");
                match engine.read_compute_storage_data::<f32>("interactive_job") {
                    Ok(results) => {
                        println!("✅ [R] Successfully read back {} elements from GPU", results.len());

                        // Display first 5 computed values
                        if results.len() >= 5 {
                            println!("🔍 [R] First 5 computed values:");
                            for (i, &value) in results.iter().take(5).enumerate() {
                                println!("   [R] results[{}] = {:.1}", i, value);
                            }
                            println!("✅ [R] GPU computation completed successfully!");
                        }
                    }
                    Err(e) => {
                        println!("❌ [R] Failed to read back GPU results: {}", e);
                    }
                }
            }
            Err(e) => {
                println!("❌ [R] Failed to execute compute job: {}", e);
                println!("💡 [R] Make sure to press 'I' first to create the job");
            }
        }

        timing_state.last_compute_run_time = Some(now);
    }
}

/// Sets up key bindings for interactive compute functionality
pub fn setup_compute_key_bindings(engine: &mut Engine) {
    engine.bind_action("init_compute", game_engine::winit::keyboard::KeyCode::KeyI);
    engine.bind_action("run_compute", game_engine::winit::keyboard::KeyCode::KeyR);
}
