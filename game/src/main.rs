use game_engine::{init, Engine, EngineConfig, FullscreenMode, GraphicsBackend, Mesh, RendererConfig, VSyncMode, VrConfig, VrSceneData, VrDrawCommand};
use glam::{Mat4, Vec3};
use winit::event_loop::EventLoop;
use winit::event::{Event, WindowEvent};
mod interactive_compute;
mod profiler_manager;

/// Main game entry point
fn main() -> anyhow::Result<()> {
    init()?;

    let event_loop = EventLoop::new()?;

    let engine_config = EngineConfig {
        renderer_config: RendererConfig {
            graphics_backend: GraphicsBackend::Vulkan,
            vsync_mode: VSyncMode::Off,
            ..Default::default()
        },
        fullscreen_mode: FullscreenMode::Windowed,
        window_title: "Game Engine VR Test".to_string(),
        // VR configuration - enabled for testing
        vr_config: VrConfig {
            enabled: false,
            application_name: "Game Engine VR Test".to_string(),
            engine_name: "Game Engine".to_string(),
            ..Default::default()
        },
        ..Default::default()
    };

    let mut engine = pollster::block_on(Engine::new_with_config(&event_loop, engine_config))?;

    // Print engine information
    println!("Engine initialized successfully!");
    println!("Graphics Backend: {:?}", engine.graphics_backend());
    println!("Available Backends: {:?}", engine.available_backends());
    println!("VSync Mode: {:?}", engine.vsync_mode());
    println!("VR Enabled: {}", engine.is_vr_enabled());
    if engine.is_vr_enabled() {
        println!("VR Session Running: {}", engine.is_vr_session_running());
    }

    // Set up profiler manager
    let mut profiler_manager = profiler_manager::ProfilerManager::new("Game Engine".to_string());

    // Load meshes
    let cube_mesh_id: u64 = engine.load_mesh_with_vram_tracking(Mesh::create_cube());
    let sphere_mesh_id: u64 = engine.load_mesh_with_vram_tracking(Mesh::create_sphere_default());

    // Dynamic sphere spawning system
    let mut random_spheres: Vec<(Vec3, f32)> = Vec::new();

    // Mouse control settings
    let mouse_sensitivity = 0.002f32;

    // Camera movement settings
    let camera_movement_speed = 5.0f32; // units per second

    // Set up keybindings
    engine.bind_action("ungrab_mouse", game_engine::winit::keyboard::KeyCode::Escape);
    engine.bind_action("switch_shader", game_engine::winit::keyboard::KeyCode::KeyH);
    engine.bind_action("toggle_culling", game_engine::winit::keyboard::KeyCode::KeyC);
    engine.bind_action("move_forward", game_engine::winit::keyboard::KeyCode::KeyW);
    engine.bind_action("move_backward", game_engine::winit::keyboard::KeyCode::KeyS);
    engine.bind_action("move_left", game_engine::winit::keyboard::KeyCode::KeyA);
    engine.bind_action("move_right", game_engine::winit::keyboard::KeyCode::KeyD);

    // Set up interactive compute key bindings
    interactive_compute::setup_compute_key_bindings(&mut engine);



    // Configure CPU frame ahead
    {
        use game_engine::graphics::CpuFrameAhead;
        engine.set_cpu_frame_ahead(CpuFrameAhead::new(0));
    }

    // Set up camera
    let mut camera = game_engine::Camera::new(16.0 / 9.0);
    camera.set_transform_degrees(
        game_engine::glam::Vec3::new(0.0, 2.0, 8.0),
        -10.0, 0.0, 0.0
    );
    camera.set_fov_degrees(60.0);
    camera.set_clipping_planes(0.05, 1000.0);

    // Set up interactive compute timing state
    let mut compute_timing_state = interactive_compute::ComputeTimingState::new();

    event_loop.run(move |event, elwt| {
        // Handle mouse input BEFORE engine.handle_event to avoid timing issues
        match &event {
            Event::WindowEvent { event: WindowEvent::MouseInput { button, state, .. }, .. } => {
                use game_engine::winit::event::{MouseButton, ElementState};

                if *button == MouseButton::Left && *state == ElementState::Pressed {
                    if !engine.is_mouse_grabbed() {
                        if let Err(e) = engine.grab_mouse() {
                            println!("Failed to grab mouse: {}", e);
                        }
                    }
                }
            }
            _ => {}
        }

        engine.handle_event(&event);

        match &event {
            Event::WindowEvent {
                event: WindowEvent::RedrawRequested,
                ..
            } => {

                // Game update
                {
                    let _delta_time = engine.time_manager.delta_time_f32();
                    profiler_manager.begin_game_update_profiling(&mut engine);
                    profiler_manager.end_game_update_profiling(&mut engine);
                }

                // Handle input BEFORE rendering so camera changes apply immediately
                {
                    let delta_time = engine.time_manager.delta_time_f32();

                    // Mouse look (only when mouse is grabbed)
                    if engine.is_mouse_grabbed() {
                        // Read mouse delta
                        let mouse_delta = engine.mouse_delta();

                        // Apply camera rotation
                        if mouse_delta.0 != 0.0f64 || mouse_delta.1 != 0.0f64 {
                            let yaw = -mouse_delta.0 as f32 * mouse_sensitivity;
                            let pitch = -mouse_delta.1 as f32 * mouse_sensitivity;
                            camera.rotate_euler_clamped(pitch, yaw, 0.0); // pitch, yaw, roll with pitch clamping
                        }

                        // Reset mouse to center of screen every frame to prevent cursor hitting window boundaries
                        if let Err(e) = engine.reset_mouse_to_center() {
                            println!("Failed to reset mouse position: {}", e);
                        }
                    }

                    // Camera movement - calculate directions once for efficiency
                    let mut movement_direction = Vec3::ZERO;

                    if engine.is_action_pressed("move_forward") {
                        movement_direction += camera.forward();
                    }

                    if engine.is_action_pressed("move_backward") {
                        movement_direction -= camera.forward();
                    }

                    if engine.is_action_pressed("move_right") {
                        movement_direction -= camera.right();
                    }

                    if engine.is_action_pressed("move_left") {
                        movement_direction += camera.right();
                    }

                    // Normalize movement direction to ensure consistent speed in all directions
                    // This prevents diagonal movement (W+D) from being faster than single direction (W)
                    if movement_direction != Vec3::ZERO {
                        let normalized_direction = movement_direction.normalize();
                        let movement_distance = camera_movement_speed * delta_time;
                        let movement_vector = normalized_direction * movement_distance;
                        camera.translate(movement_vector);
                    }

                    // Ungrab mouse on ESC key
                    if engine.is_action_pressed("ungrab_mouse") && engine.is_mouse_grabbed() {
                        if let Err(e) = engine.ungrab_mouse() {
                            println!("Failed to ungrab mouse: {}", e);
                        }
                    }
                }

                // 3D Rendering
                {
                    let time = engine.time_manager.total_time_f32();
                    let size = engine.renderer.size();

                    profiler_manager.begin_3d_rendering_profiling(&mut engine);

                    engine.set_clear_background(true);
                    engine.set_background_color([0.1, 0.2, 0.3, 1.0]);
                    engine.set_clear_depth(true);

                    let aspect_ratio = size.width as f32 / size.height as f32;
                    camera.set_aspect_ratio(aspect_ratio);
                    let view_proj = camera.view_projection_matrix();

                    let renderer_3d = engine.renderer_3d();
                    renderer_3d.clear_frame();
                    renderer_3d.set_camera(view_proj);

                    // Draw objects
                    let rotation1 = Mat4::from_rotation_y(time) * Mat4::from_rotation_x(time * 0.5);
                    renderer_3d.draw_mesh(cube_mesh_id, rotation1);

                    let translation2 = Mat4::from_translation(glam::Vec3::new(8.0, 0.0, 0.0));
                    let scale2 = Mat4::from_scale(glam::Vec3::new(2.0, 2.0, 2.0));
                    let rotation2 = Mat4::from_rotation_x(time * 0.8) * Mat4::from_rotation_z(time * 0.3);
                    let transform2 = translation2 * scale2 * rotation2;
                    renderer_3d.draw_mesh(cube_mesh_id, transform2);

                    let translation3 = Mat4::from_translation(glam::Vec3::new(-6.0, 0.0, 0.0));
                    let scale3 = Mat4::from_scale(glam::Vec3::new(1.5, 1.5, 1.5));
                    let rotation3 = Mat4::from_rotation_y(time * 1.2) * Mat4::from_rotation_z(time * 0.6);
                    let transform3 = translation3 * scale3 * rotation3;
                    renderer_3d.draw_mesh(sphere_mesh_id, transform3);

                    // 10x10 grid of spheres
                    let grid_size = 10;
                    let spacing = 2.5;
                    let grid_offset = Vec3::new(0.0, 0.0, -15.0);

                    for row in 0..grid_size {
                        for col in 0..grid_size {
                            let x = (col as f32 - (grid_size as f32 - 1.0) / 2.0) * spacing;
                            let y = (row as f32 - (grid_size as f32 - 1.0) / 2.0) * spacing;
                            let position = grid_offset + Vec3::new(x, y, 0.0);

                            let time_offset = (row * grid_size + col) as f32 * 0.2;
                            let rotation_speed = 0.5 + (row + col) as f32 * 0.1;

                            let translation = Mat4::from_translation(position);
                            let scale = Mat4::from_scale(Vec3::splat(0.8));
                            let rotation = Mat4::from_rotation_y((time + time_offset) * rotation_speed)
                                         * Mat4::from_rotation_x((time + time_offset) * rotation_speed * 0.7);

                            let transform = translation * scale * rotation;
                            renderer_3d.draw_mesh(sphere_mesh_id, transform);
                        }
                    }

                    if random_spheres.len() < 1_000 {
                        for _ in 0..250 {
                            let random_x = (rand::random::<f32>() - 0.5) * 100.0;
                            let random_y = (rand::random::<f32>() - 0.5) * 100.0;
                            let random_z = (rand::random::<f32>() - 0.5) * 100.0;
                            let position = Vec3::new(random_x, random_y, random_z);
                            let rotation_speed = 0.5 + rand::random::<f32>() * 1.5;
                            random_spheres.push((position, rotation_speed));
                        }
                    }

                    // Render dynamically spawned spheres
                    for (index, (position, rotation_speed)) in random_spheres.iter().enumerate() {
                        let time_offset = index as f32 * 0.3;
                        let translation = Mat4::from_translation(*position);
                        let scale = Mat4::from_scale(Vec3::splat(1.2));
                        let rotation = Mat4::from_rotation_y((time + time_offset) * rotation_speed)
                                     * Mat4::from_rotation_x((time + time_offset) * rotation_speed * 0.8);

                        let transform = translation * scale * rotation;
                        renderer_3d.draw_mesh(sphere_mesh_id, transform);
                    }

                    profiler_manager.end_3d_rendering_profiling(&mut engine);

                    // Collect VR scene data if VR is enabled
                    if engine.vr_renderer.is_enabled() {
                        let mut vr_draw_commands = Vec::new();

                        // Add all the same draw commands for VR
                        vr_draw_commands.push(VrDrawCommand {
                            mesh_id: cube_mesh_id,
                            transform: rotation1,
                        });

                        vr_draw_commands.push(VrDrawCommand {
                            mesh_id: cube_mesh_id,
                            transform: transform2,
                        });

                        vr_draw_commands.push(VrDrawCommand {
                            mesh_id: sphere_mesh_id,
                            transform: transform3,
                        });

                        // Add grid spheres
                        for row in 0..grid_size {
                            for col in 0..grid_size {
                                let x = (col as f32 - (grid_size as f32 - 1.0) / 2.0) * spacing;
                                let y = (row as f32 - (grid_size as f32 - 1.0) / 2.0) * spacing;
                                let position = grid_offset + Vec3::new(x, y, 0.0);

                                let time_offset = (row * grid_size + col) as f32 * 0.2;
                                let rotation_speed = 0.5 + (row + col) as f32 * 0.1;

                                let translation = Mat4::from_translation(position);
                                let scale = Mat4::from_scale(Vec3::splat(0.8));
                                let rotation = Mat4::from_rotation_y((time + time_offset) * rotation_speed)
                                             * Mat4::from_rotation_x((time + time_offset) * rotation_speed * 0.7);

                                let transform = translation * scale * rotation;
                                vr_draw_commands.push(VrDrawCommand {
                                    mesh_id: sphere_mesh_id,
                                    transform,
                                });
                            }
                        }

                        // Add random spheres
                        for (index, (position, rotation_speed)) in random_spheres.iter().enumerate() {
                            let time_offset = index as f32 * 0.3;
                            let translation = Mat4::from_translation(*position);
                            let scale = Mat4::from_scale(Vec3::splat(1.2));
                            let rotation = Mat4::from_rotation_y((time + time_offset) * rotation_speed)
                                         * Mat4::from_rotation_x((time + time_offset) * rotation_speed * 0.8);

                            let transform = translation * scale * rotation;
                            vr_draw_commands.push(VrDrawCommand {
                                mesh_id: sphere_mesh_id,
                                transform,
                            });
                        }

                        // Create VR scene data
                        let vr_scene_data = VrSceneData {
                            view_projection: view_proj, // Use the same camera for now
                            background_color: [0.0, 0.5, 0.2, 1.0], // Nice green background
                            clear_background: true,
                            draw_commands: vr_draw_commands,
                        };

                        // Pass scene data to VR renderer
                        engine.vr_renderer.set_scene_data(vr_scene_data);
                    }
                }

                // Debug toggles
                static mut LAST_CULLING_TOGGLE_TIME: Option<std::time::Instant> = None;

                if engine.is_action_pressed("toggle_culling") {
                    let now = std::time::Instant::now();
                    let should_toggle = unsafe {
                        if let Some(last_time) = LAST_CULLING_TOGGLE_TIME {
                            now.duration_since(last_time) > std::time::Duration::from_millis(500)
                        } else {
                            true
                        }
                    };

                    if should_toggle {
                        engine.toggle_face_culling();
                        unsafe {
                            LAST_CULLING_TOGGLE_TIME = Some(now);
                        }
                    }
                }

                if engine.is_action_pressed("init_compute") {
                    interactive_compute::handle_init_compute_action(&mut engine, &mut compute_timing_state);
                }

                if engine.is_action_pressed("run_compute") {
                    interactive_compute::handle_run_compute_action(&mut engine, &mut compute_timing_state);
                }

                // Shader switching
                static mut CURRENT_SHADER: &str = "basic";
                static mut LAST_SWITCH_TIME: Option<std::time::Instant> = None;

                if engine.is_action_pressed("switch_shader") {
                    let now = std::time::Instant::now();
                    let should_switch = unsafe {
                        if let Some(last_time) = LAST_SWITCH_TIME {
                            now.duration_since(last_time) > std::time::Duration::from_millis(500)
                        } else {
                            true
                        }
                    };

                    if should_switch {
                        unsafe {
                            CURRENT_SHADER = if CURRENT_SHADER == "basic" {
                                engine.switch_shader_program("random_color");
                                "random_color"
                            } else {
                                engine.switch_shader_program("basic");
                                "basic"
                            };
                            LAST_SWITCH_TIME = Some(now);
                        }
                    }
                }


            }
            _ => {}
        }

        // Shader hot-reload
        static mut LAST_HOTRELOAD_CHECK: Option<std::time::Instant> = None;
        let hotreload_check_interval = std::time::Duration::from_secs(1);

        let now = std::time::Instant::now();
        let should_check_hotreload = unsafe {
            if let Some(last_time) = LAST_HOTRELOAD_CHECK {
                now.duration_since(last_time) >= hotreload_check_interval
            } else {
                true
            }
        };

        if should_check_hotreload {
            engine.check_shader_updates();
            unsafe {
                LAST_HOTRELOAD_CHECK = Some(now);
            }
        }

        // Update profiler-related functionality (debug output and FPS tracking)
        profiler_manager.update(&mut engine);

        if !engine.running {
            elwt.exit();
        }
    }).unwrap();

    Ok(())
}
